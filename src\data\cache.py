"""
Data caching system with TTL and memory management
"""

import gc
import time
import logging
import threading
from typing import Any, Dict, Optional, Tuple
from collections import OrderedDict

logger = logging.getLogger(__name__)


class DataCache:
    """TTL-based cache with automatic cleanup and memory management"""

    def __init__(self, default_ttl: int = 300, max_size: int = 1000, gc_interval: int = 300,
                 max_memory_mb: int = 512, memory_check_interval: int = 300, memory_cleanup_threshold: float = 0.8):
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.gc_interval = gc_interval  # Garbage collection interval in seconds
        self.max_memory_mb = max_memory_mb  # Maximum memory usage in MB
        self.memory_check_interval = memory_check_interval  # Memory check interval in seconds
        self.memory_cleanup_threshold = memory_cleanup_threshold  # Memory cleanup threshold (0.8 = 80%)
        self.cache: OrderedDict[str, Tuple[Any, float, float]] = OrderedDict()  # key -> (data, timestamp, ttl)
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'cleanups': 0,
            'gc_runs': 0,
            'memory_freed_mb': 0.0,
            'memory_cleanups': 0
        }
        self.last_cleanup = time.time()
        self.last_gc = time.time()
        self.last_memory_check = time.time()
        self.cleanup_interval = 60  # Cleanup every minute
        self._lock = threading.RLock()  # Thread-safe operations
    
    def _cleanup_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        
        # Only cleanup if enough time has passed
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        expired_keys = []
        for key, (_, timestamp, ttl) in self.cache.items():
            if current_time - timestamp > ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self.stats['cleanups'] += 1
            logger.debug(f"🧹 Cache cleanup: removed {len(expired_keys)} expired entries")
        
        self.last_cleanup = current_time

    def _run_garbage_collection(self):
        """Run garbage collection to free memory"""
        current_time = time.time()

        # Only run GC if enough time has passed
        if current_time - self.last_gc < self.gc_interval:
            return

        # Get memory usage before GC
        memory_before = 0
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
        except ImportError:
            pass  # psutil not available, continue without memory tracking

        # Force garbage collection
        collected = gc.collect()

        # Get memory usage after GC
        memory_freed = 0
        if memory_before > 0:
            try:
                memory_after = process.memory_info().rss / 1024 / 1024  # MB
                memory_freed = max(0, memory_before - memory_after)
                self.stats['memory_freed_mb'] += memory_freed
            except (NameError, ImportError):
                pass

        self.stats['gc_runs'] += 1
        self.last_gc = current_time

        if collected > 0 or memory_freed > 1:  # Log if significant cleanup occurred
            logger.debug(f"🗑️ Garbage collection: collected {collected} objects, "
                        f"freed {memory_freed:.1f}MB memory")

    def _check_memory_usage(self):
        """Check memory usage and perform cleanup if necessary"""
        current_time = time.time()

        # Only check memory if enough time has passed
        if current_time - self.last_memory_check < self.memory_check_interval:
            return

        memory_stats = self.get_memory_usage()
        current_memory_mb = memory_stats['rss_mb']

        # Check if memory usage exceeds threshold
        memory_threshold_mb = self.max_memory_mb * self.memory_cleanup_threshold

        if current_memory_mb > memory_threshold_mb:
            logger.warning(f"⚠️ Memory usage high: {current_memory_mb:.1f}MB > {memory_threshold_mb:.1f}MB threshold")

            # Aggressive cleanup: remove 25% of cache entries (oldest first)
            entries_to_remove = max(1, len(self.cache) // 4)
            removed_count = 0

            # Remove oldest entries
            keys_to_remove = list(self.cache.keys())[:entries_to_remove]
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                    removed_count += 1

            # Force garbage collection
            self.force_garbage_collection()

            self.stats['memory_cleanups'] += 1
            logger.info(f"🧹 Memory cleanup: removed {removed_count} cache entries due to high memory usage")

        self.last_memory_check = current_time

    def _evict_lru(self):
        """Evict least recently used entries if cache is full"""
        while len(self.cache) >= self.max_size:
            # Remove oldest entry (LRU)
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            self.stats['evictions'] += 1
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached data if not expired"""
        with self._lock:
            self._cleanup_expired()
            self._run_garbage_collection()
            self._check_memory_usage()

            if key not in self.cache:
                self.stats['misses'] += 1
                return None

            data, timestamp, ttl = self.cache[key]
            current_time = time.time()

            # Check if expired
            if current_time - timestamp > ttl:
                del self.cache[key]
                self.stats['misses'] += 1
                return None

            # Move to end (mark as recently used)
            self.cache.move_to_end(key)
            self.stats['hits'] += 1
            return data
    
    def set(self, key: str, data: Any, ttl: Optional[int] = None):
        """Set cached data with TTL"""
        with self._lock:
            if ttl is None:
                ttl = self.default_ttl

            current_time = time.time()

            # Evict LRU entries if cache is full
            self._evict_lru()

            # Store data with timestamp and TTL
            self.cache[key] = (data, current_time, ttl)

            # Move to end (mark as recently used)
            self.cache.move_to_end(key)
    
    def delete(self, key: str) -> bool:
        """Delete cached entry"""
        with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False

    def clear(self):
        """Clear all cached data"""
        with self._lock:
            self.cache.clear()
            # Force garbage collection after clearing cache
            gc.collect()
            logger.info("🧹 Cache cleared and garbage collected")

    def force_garbage_collection(self):
        """Force immediate garbage collection"""
        with self._lock:
            # Temporarily reset last_gc to force immediate collection
            self.last_gc = 0
            self._run_garbage_collection()
            # Let the GC run update the last_gc time

    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics"""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                'percent': process.memory_percent()
            }
        except ImportError:
            return {'rss_mb': 0, 'vms_mb': 0, 'percent': 0}

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            memory_stats = self.get_memory_usage()

            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'hit_rate': f"{hit_rate:.1f}%",
                'evictions': self.stats['evictions'],
                'cleanups': self.stats['cleanups'],
                'gc_runs': self.stats['gc_runs'],
                'memory_cleanups': self.stats['memory_cleanups'],
                'memory_freed_mb': f"{self.stats['memory_freed_mb']:.1f}MB",
                'current_memory_mb': f"{memory_stats['rss_mb']:.1f}MB",
                'memory_percent': f"{memory_stats['percent']:.1f}%",
                'max_memory_mb': f"{self.max_memory_mb}MB"
            }
    
    def log_stats(self):
        """Log cache statistics"""
        stats = self.get_stats()
        logger.info(f"📋 Cache stats: {stats['size']}/{stats['max_size']} entries, "
                   f"{stats['hit_rate']} hit rate, {stats['evictions']} evictions, "
                   f"{stats['gc_runs']} GC runs, {stats['memory_freed_mb']} freed, "
                   f"current: {stats['current_memory_mb']} ({stats['memory_percent']})")


class CacheKey:
    """Helper class for generating consistent cache keys"""
    
    @staticmethod
    def ohlcv(symbol: str, timeframe: str, limit: int) -> str:
        """Generate cache key for OHLCV data"""
        return f"ohlcv:{symbol}:{timeframe}:{limit}"
    
    @staticmethod
    def funding_rate(symbol: str) -> str:
        """Generate cache key for funding rate"""
        return f"funding:{symbol}"
    
    @staticmethod
    def funding_history(symbol: str, limit: int) -> str:
        """Generate cache key for funding rate history"""
        return f"funding_history:{symbol}:{limit}"
    
    @staticmethod
    def ticker(symbol: str) -> str:
        """Generate cache key for ticker data"""
        return f"ticker:{symbol}"
    
    @staticmethod
    def markets(exchange: str) -> str:
        """Generate cache key for markets data"""
        return f"markets:{exchange}"
    
    @staticmethod
    def volume(symbol: str, days: int) -> str:
        """Generate cache key for volume data"""
        return f"volume:{symbol}:{days}d"
    
    @staticmethod
    def volatility(symbol: str, days: int) -> str:
        """Generate cache key for volatility data"""
        return f"volatility:{symbol}:{days}d"


# Global cache instance with garbage collection enabled
global_cache = DataCache(
    default_ttl=300,      # 5 minutes default TTL
    max_size=1000,        # Maximum 1000 entries
    gc_interval=300       # Garbage collection every 5 minutes
)


def get_global_cache() -> DataCache:
    """Get the global cache instance"""
    return global_cache


def configure_global_cache(default_ttl: int = 300, max_size: int = 1000, gc_interval: int = 300,
                          max_memory_mb: int = 512, memory_check_interval: int = 300, memory_cleanup_threshold: float = 0.8):
    """Configure the global cache with custom parameters including memory management"""
    global global_cache
    global_cache = DataCache(
        default_ttl=default_ttl,
        max_size=max_size,
        gc_interval=gc_interval,
        max_memory_mb=max_memory_mb,
        memory_check_interval=memory_check_interval,
        memory_cleanup_threshold=memory_cleanup_threshold
    )
    logger.info(f"🔧 Global cache configured: TTL={default_ttl}s, max_size={max_size}, GC_interval={gc_interval}s, "
               f"max_memory={max_memory_mb}MB, memory_check={memory_check_interval}s, cleanup_threshold={memory_cleanup_threshold*100:.0f}%")
