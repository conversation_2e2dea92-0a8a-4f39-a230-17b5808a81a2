"""
Enhanced error handling for API rate limits and network issues
"""

import asyncio
import logging
import random
import time
from typing import Dict, Any, Optional, Callable, Union
from functools import wraps

from .exceptions import (
    RateLimitError, NetworkError, ExchangeMaintenanceError,
    CircuitBreakerError, APIError, StatArbError,
    InsufficientBalanceError, InvalidOrderError, MarketClosedError
)

logger = logging.getLogger(__name__)


class ErrorClassifier:
    """Classifies different types of API and network errors"""
    
    # Rate limit error patterns
    RATE_LIMIT_PATTERNS = [
        'rate limit', 'too many requests', 'exceeded', 'throttle', 'throttled',
        'rate_limit', 'ratelimit', 'too_many_requests', 'request_rate_limit',
        'api rate limit', 'requests per', 'per second', 'per minute',
        'quota exceeded', 'limit exceeded', 'frequency limit'
    ]
    
    # Network error patterns
    NETWORK_PATTERNS = [
        'connection', 'timeout', 'network', 'unreachable', 'refused',
        'connection reset', 'connection aborted', 'connection timeout',
        'read timeout', 'connect timeout', 'socket timeout',
        'dns', 'resolve', 'host', 'ssl', 'certificate',
        'connection error', 'network error', 'socket error',
        'temporary failure', 'service unavailable'
    ]
    
    # Exchange maintenance patterns
    MAINTENANCE_PATTERNS = [
        'maintenance', 'under maintenance', 'system maintenance',
        'scheduled maintenance', 'service unavailable', 'temporarily unavailable',
        'system upgrade', 'downtime', 'offline', 'suspended'
    ]
    
    # Insufficient balance patterns
    BALANCE_PATTERNS = [
        'insufficient', 'balance', 'not enough', 'insufficient funds',
        'insufficient balance', 'insufficient margin', 'low balance',
        'account balance', 'available balance'
    ]
    
    # Invalid order patterns
    INVALID_ORDER_PATTERNS = [
        'invalid order', 'order rejected', 'invalid price', 'invalid quantity',
        'minimum order', 'maximum order', 'order size', 'price precision',
        'quantity precision', 'lot size', 'tick size', 'invalid symbol'
    ]
    
    # Market closed patterns
    MARKET_CLOSED_PATTERNS = [
        'market closed', 'trading suspended', 'trading halted',
        'market not open', 'outside trading hours', 'market hours'
    ]
    
    @classmethod
    def classify_error(cls, error: Exception, exchange: str = None, symbol: str = None) -> Exception:
        """Classify an error and return appropriate exception type"""
        error_msg = str(error).lower()
        
        # Extract retry-after from rate limit headers if available
        retry_after = cls._extract_retry_after(error)
        
        # Check for rate limit errors
        if any(pattern in error_msg for pattern in cls.RATE_LIMIT_PATTERNS):
            return RateLimitError(
                f"Rate limit exceeded: {error}",
                exchange=exchange,
                symbol=symbol,
                retry_after=retry_after
            )
        
        # Check for network errors
        if any(pattern in error_msg for pattern in cls.NETWORK_PATTERNS):
            is_timeout = any(timeout_word in error_msg for timeout_word in ['timeout', 'timed out'])
            return NetworkError(
                f"Network error: {error}",
                exchange=exchange,
                symbol=symbol,
                is_timeout=is_timeout
            )
        
        # Check for maintenance errors
        if any(pattern in error_msg for pattern in cls.MAINTENANCE_PATTERNS):
            return ExchangeMaintenanceError(
                f"Exchange maintenance: {error}",
                exchange=exchange
            )
        
        # Check for balance errors
        if any(pattern in error_msg for pattern in cls.BALANCE_PATTERNS):
            return cls._create_api_error('InsufficientBalanceError', error, exchange, symbol)
        
        # Check for invalid order errors
        if any(pattern in error_msg for pattern in cls.INVALID_ORDER_PATTERNS):
            return cls._create_api_error('InvalidOrderError', error, exchange, symbol)
        
        # Check for market closed errors
        if any(pattern in error_msg for pattern in cls.MARKET_CLOSED_PATTERNS):
            return cls._create_api_error('MarketClosedError', error, exchange, symbol)
        
        # Default to generic API error
        return APIError(f"API error: {error}", exchange=exchange, symbol=symbol)
    
    @staticmethod
    def _extract_retry_after(error: Exception) -> Optional[int]:
        """Extract retry-after value from error if available"""
        try:
            # Try to extract from CCXT error response
            if hasattr(error, 'response') and error.response:
                headers = getattr(error.response, 'headers', {})
                if 'retry-after' in headers:
                    return int(headers['retry-after'])
                if 'Retry-After' in headers:
                    return int(headers['Retry-After'])
            
            # Try to extract from error message
            error_msg = str(error).lower()
            if 'retry after' in error_msg:
                # Extract number after "retry after"
                import re
                match = re.search(r'retry after (\d+)', error_msg)
                if match:
                    return int(match.group(1))
        except (ValueError, AttributeError):
            pass
        
        return None
    
    @staticmethod
    def _create_api_error(error_type: str, error: Exception, exchange: str, symbol: str) -> APIError:
        """Create specific API error type"""
        from .exceptions import InsufficientBalanceError, InvalidOrderError, MarketClosedError
        
        error_classes = {
            'InsufficientBalanceError': InsufficientBalanceError,
            'InvalidOrderError': InvalidOrderError,
            'MarketClosedError': MarketClosedError
        }
        
        error_class = error_classes.get(error_type, APIError)
        return error_class(f"{error_type}: {error}", exchange=exchange, symbol=symbol)


class ExponentialBackoff:
    """Implements exponential backoff with jitter for retries"""
    
    def __init__(self, base_delay: float = 1.0, max_delay: float = 300.0, 
                 multiplier: float = 2.0, jitter: bool = True):
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.multiplier = multiplier
        self.jitter = jitter
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number (0-based)"""
        delay = self.base_delay * (self.multiplier ** attempt)
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            # Add random jitter (±25% of delay)
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(0, delay)  # Ensure non-negative
        
        return delay


class CircuitBreaker:
    """Enhanced circuit breaker with half-open state and adaptive thresholds"""
    
    def __init__(self, config: Dict[str, Any]):
        self.failure_threshold = config.get('failure_threshold', 5)
        self.timeout_seconds = config.get('timeout_seconds', 60)
        self.half_open_max_calls = config.get('half_open_max_calls', 3)
        
        # State tracking
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'closed'  # closed, open, half_open
        self.half_open_calls = 0
        self.half_open_successes = 0
        
        # Adaptive thresholds
        self.consecutive_successes = 0
        self.adaptive_threshold = self.failure_threshold
    
    def is_open(self) -> bool:
        """Check if circuit breaker is open"""
        if self.state == 'open':
            # Check if timeout has passed to move to half-open
            if (time.time() - self.last_failure_time) >= self.timeout_seconds:
                self.state = 'half_open'
                self.half_open_calls = 0
                self.half_open_successes = 0
                logger.info("🔄 Circuit breaker moving to half-open state")
                return False
            return True
        
        return False
    
    def can_execute(self) -> bool:
        """Check if execution is allowed"""
        if self.state == 'closed':
            return True
        elif self.state == 'half_open':
            return self.half_open_calls < self.half_open_max_calls
        else:  # open
            return not self.is_open()
    
    def record_success(self):
        """Record successful operation"""
        if self.state == 'half_open':
            self.half_open_calls += 1
            self.half_open_successes += 1
            
            # Check if we can close the circuit
            if self.half_open_successes >= self.half_open_max_calls:
                self.state = 'closed'
                self.failure_count = 0
                self.consecutive_successes += 1
                self._adapt_threshold()
                logger.info("✅ Circuit breaker closed - service recovered")
        else:
            self.failure_count = max(0, self.failure_count - 1)
            self.consecutive_successes += 1
            self._adapt_threshold()
    
    def record_failure(self):
        """Record failed operation"""
        self.consecutive_successes = 0
        
        if self.state == 'half_open':
            self.half_open_calls += 1
            # Failure in half-open state - go back to open
            self.state = 'open'
            self.last_failure_time = time.time()
            logger.warning("⚠️ Circuit breaker reopened due to failure in half-open state")
        else:
            self.failure_count += 1
            if self.failure_count >= self.adaptive_threshold:
                self.state = 'open'
                self.last_failure_time = time.time()
                logger.warning(f"🚫 Circuit breaker opened after {self.failure_count} failures")
    
    def _adapt_threshold(self):
        """Adapt failure threshold based on recent performance"""
        if self.consecutive_successes > 50:
            # Increase threshold if we've had many successes
            self.adaptive_threshold = min(self.failure_threshold * 2, 20)
        elif self.consecutive_successes < 10:
            # Decrease threshold if we're having issues
            self.adaptive_threshold = max(self.failure_threshold // 2, 2)
        else:
            # Reset to default
            self.adaptive_threshold = self.failure_threshold
    
    def get_status(self) -> Dict[str, Any]:
        """Get current circuit breaker status"""
        return {
            'state': self.state,
            'failure_count': self.failure_count,
            'adaptive_threshold': self.adaptive_threshold,
            'consecutive_successes': self.consecutive_successes,
            'can_execute': self.can_execute()
        }


class ErrorRecoveryManager:
    """Manages error recovery strategies and retry logic"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.circuit_breaker = CircuitBreaker(config.get('circuit_breaker', {}))
        self.backoff = ExponentialBackoff(
            base_delay=config.get('retry_base_delay', 1.0),
            max_delay=config.get('retry_max_delay', 300.0),
            multiplier=config.get('retry_multiplier', 2.0),
            jitter=config.get('retry_jitter', True)
        )

        # Retry configuration
        self.max_retries = config.get('max_retries', 3)
        self.rate_limit_max_retries = config.get('rate_limit_max_retries', 5)
        self.network_max_retries = config.get('network_max_retries', 3)

        # Error tracking
        self.error_counts = {}
        self.last_errors = {}

    async def execute_with_retry(self,
                               func: Callable,
                               *args,
                               exchange: str = None,
                               symbol: str = None,
                               operation: str = None,
                               **kwargs) -> Any:
        """Execute function with comprehensive retry logic"""

        if not self.circuit_breaker.can_execute():
            raise CircuitBreakerError(
                "Circuit breaker is open - service unavailable",
                retry_after=self.circuit_breaker.timeout_seconds
            )

        last_exception = None
        operation_key = f"{exchange}:{operation}:{symbol}" if all([exchange, operation, symbol]) else "unknown"

        # Determine max retries based on operation type
        max_retries = self.max_retries

        for attempt in range(max_retries + 1):
            try:
                # Check circuit breaker before each attempt
                if not self.circuit_breaker.can_execute():
                    raise CircuitBreakerError("Circuit breaker opened during retry")

                # Execute the function
                result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)

                # Success - record and return
                self.circuit_breaker.record_success()
                self._record_success(operation_key)
                return result

            except Exception as e:
                last_exception = e
                classified_error = ErrorClassifier.classify_error(e, exchange, symbol)

                # Record failure
                self.circuit_breaker.record_failure()
                self._record_error(operation_key, classified_error)

                # Log the error
                self._log_error(classified_error, attempt, max_retries, operation_key)

                # Check if we should retry
                if attempt >= max_retries:
                    logger.error(f"❌ {operation_key}: Max retries ({max_retries}) exceeded")
                    break

                # Calculate retry delay
                retry_delay = await self._calculate_retry_delay(classified_error, attempt)

                if retry_delay is None:
                    # Non-retryable error
                    logger.error(f"❌ {operation_key}: Non-retryable error - {classified_error}")
                    break

                # Wait before retry
                if retry_delay > 0:
                    logger.info(f"⏳ {operation_key}: Retrying in {retry_delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(retry_delay)

        # All retries exhausted
        raise last_exception

    async def _calculate_retry_delay(self, error: Exception, attempt: int) -> Optional[float]:
        """Calculate retry delay based on error type"""

        if isinstance(error, RateLimitError):
            # Use retry-after header if available, otherwise exponential backoff
            if error.retry_after:
                return float(error.retry_after)
            else:
                return self.backoff.calculate_delay(attempt) * 2  # Longer delays for rate limits

        elif isinstance(error, NetworkError):
            if error.is_timeout:
                # Shorter delays for timeouts
                return self.backoff.calculate_delay(attempt) * 0.5
            else:
                # Standard delay for other network errors
                return self.backoff.calculate_delay(attempt)

        elif isinstance(error, ExchangeMaintenanceError):
            # Longer delays for maintenance
            base_delay = error.estimated_duration or 300  # 5 minutes default
            return base_delay + self.backoff.calculate_delay(attempt)

        elif isinstance(error, (APIError,)):
            # Check if it's a retryable API error
            if isinstance(error, (InsufficientBalanceError, InvalidOrderError, MarketClosedError)):
                # These are typically not retryable
                return None
            else:
                # Generic API error - use standard backoff
                return self.backoff.calculate_delay(attempt)

        else:
            # Unknown error type - use standard backoff
            return self.backoff.calculate_delay(attempt)

    def _record_error(self, operation_key: str, error: Exception):
        """Record error for tracking"""
        if operation_key not in self.error_counts:
            self.error_counts[operation_key] = {}

        error_type = type(error).__name__
        self.error_counts[operation_key][error_type] = self.error_counts[operation_key].get(error_type, 0) + 1
        self.last_errors[operation_key] = {
            'error': str(error),
            'type': error_type,
            'timestamp': time.time()
        }

    def _record_success(self, operation_key: str):
        """Record successful operation"""
        # Clear error tracking on success
        if operation_key in self.last_errors:
            del self.last_errors[operation_key]

    def _log_error(self, error: Exception, attempt: int, max_retries: int, operation_key: str):
        """Log error with appropriate level"""
        error_type = type(error).__name__

        if isinstance(error, RateLimitError):
            logger.warning(f"⚠️ {operation_key}: Rate limit hit (attempt {attempt + 1}/{max_retries + 1}) - {error}")
        elif isinstance(error, NetworkError):
            logger.warning(f"⚠️ {operation_key}: Network error (attempt {attempt + 1}/{max_retries + 1}) - {error}")
        elif isinstance(error, ExchangeMaintenanceError):
            logger.warning(f"⚠️ {operation_key}: Exchange maintenance (attempt {attempt + 1}/{max_retries + 1}) - {error}")
        else:
            logger.warning(f"⚠️ {operation_key}: {error_type} (attempt {attempt + 1}/{max_retries + 1}) - {error}")

    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        return {
            'error_counts': self.error_counts,
            'last_errors': self.last_errors,
            'circuit_breaker': self.circuit_breaker.get_status()
        }


def enhanced_retry(max_retries: int = None,
                  exchange: str = None,
                  operation: str = None,
                  error_manager: ErrorRecoveryManager = None):
    """Enhanced retry decorator with sophisticated error handling"""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Extract symbol from args/kwargs if possible
            symbol = kwargs.get('symbol') or (args[0] if args and isinstance(args[0], str) else None)

            # Use provided error manager or create default one
            manager = error_manager or ErrorRecoveryManager({})

            return await manager.execute_with_retry(
                func, *args,
                exchange=exchange,
                symbol=symbol,
                operation=operation or func.__name__,
                **kwargs
            )

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For sync functions, run the async wrapper
            return asyncio.run(async_wrapper(*args, **kwargs))

        # Return appropriate wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
