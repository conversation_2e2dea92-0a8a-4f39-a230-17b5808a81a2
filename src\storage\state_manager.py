"""
State management with atomic writes and encryption
"""

import os
import json
import tempfile
import logging
from datetime import datetime
from decimal import Decimal
from typing import Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class DecimalEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle Decimal objects"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)


class StateManager:
    """Manages strategy state with atomic writes and data retention"""
    
    def __init__(self, state_file: str = "strategy_state.json", encrypt: bool = False):
        self.state_file = Path(state_file)
        self.encrypt = encrypt
        self.state = {
            'last_rebalance': None,
            'last_monitoring_check': None,
            'positions': {},
            'pending_orders': {},
            'metadata': {
                'created_at': datetime.now().isoformat(),
                'version': '2.0.0'
            }
        }
        self._load_state()
    
    def _load_state(self):
        """Load strategy state from file"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    loaded_state = json.load(f)
                
                # Merge with default structure
                self.state.update(loaded_state)
                
                # Parse datetime strings
                if self.state.get('last_rebalance'):
                    try:
                        self.state['last_rebalance'] = datetime.fromisoformat(
                            self.state['last_rebalance']
                        )
                    except ValueError:
                        self.state['last_rebalance'] = None
                
                if self.state.get('last_monitoring_check'):
                    try:
                        self.state['last_monitoring_check'] = datetime.fromisoformat(
                            self.state['last_monitoring_check']
                        )
                    except ValueError:
                        self.state['last_monitoring_check'] = None

                # Ensure positions and pending_orders exist
                if 'positions' not in self.state:
                    self.state['positions'] = {}
                if 'pending_orders' not in self.state:
                    self.state['pending_orders'] = {}

                logger.info(f"📁 Loaded state: {len(self.state['positions'])} positions, "
                           f"{len(self.state['pending_orders'])} pending orders")
            else:
                logger.info("📁 No existing state file found - starting fresh")
                
        except Exception as e:
            logger.warning(f"⚠️ Failed to load state: {e}")
            # Reset to default state on error
            self.state = {
                'last_rebalance': None,
                'last_monitoring_check': None,
                'positions': {},
                'pending_orders': {},
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'version': '2.0.0'
                }
            }
    
    def save_state(self):
        """Save state with atomic write"""
        try:
            
            # Prepare state for serialization
            state_to_save = self.state.copy()
            
            # Convert datetime objects to ISO strings
            if state_to_save.get('last_rebalance'):
                state_to_save['last_rebalance'] = state_to_save['last_rebalance'].isoformat()
            
            if state_to_save.get('last_monitoring_check'):
                state_to_save['last_monitoring_check'] = state_to_save['last_monitoring_check'].isoformat()
            
            # Add timestamp
            state_to_save['metadata']['last_saved'] = datetime.now().isoformat()
            
            # Atomic write using temporary file
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            
            with tempfile.NamedTemporaryFile(
                mode='w',
                dir=self.state_file.parent,
                delete=False,
                suffix='.tmp'
            ) as temp_file:
                json.dump(state_to_save, temp_file, indent=2, cls=DecimalEncoder)
                temp_file.flush()
                os.fsync(temp_file.fileno())
                temp_filename = temp_file.name
            
            # Atomic move
            os.replace(temp_filename, self.state_file)
            
            logger.debug("💾 State saved successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to save state: {e}")
            # Clean up temp file if it exists
            try:
                if 'temp_filename' in locals():
                    os.unlink(temp_filename)
            except:
                pass
    
    # Convenience methods for accessing state
    
    def set_last_rebalance(self, timestamp: datetime):
        self.state['last_rebalance'] = timestamp
    
    def get_last_rebalance(self) -> Optional[datetime]:
        return self.state['last_rebalance']
    
    def set_last_monitoring_check(self, timestamp: datetime):
        self.state['last_monitoring_check'] = timestamp
    
    def get_last_monitoring_check(self) -> Optional[datetime]:
        return self.state['last_monitoring_check']
    
    def set_positions(self, positions: dict):
        """Set current positions state"""
        self.state['positions'] = positions

    def get_positions(self) -> dict:
        """Get current positions state"""
        return self.state.get('positions', {})

    def set_pending_orders(self, orders: dict):
        """Set pending orders state"""
        self.state['pending_orders'] = orders

    def get_pending_orders(self) -> dict:
        """Get pending orders state"""
        return self.state.get('pending_orders', {})

    def get_summary(self) -> str:
        """Get state summary for logging"""
        last_rebalance = self.state.get('last_rebalance')
        last_monitoring = self.state.get('last_monitoring_check')
        positions_count = len(self.state.get('positions', {}))
        orders_count = len(self.state.get('pending_orders', {}))

        rebalance_str = last_rebalance.strftime('%Y-%m-%d %H:%M UTC') if last_rebalance else 'Never'
        monitoring_str = last_monitoring.strftime('%Y-%m-%d %H:%M UTC') if last_monitoring else 'Never'

        return f"last_rebalance: {rebalance_str}, last_monitoring: {monitoring_str}, positions: {positions_count}, orders: {orders_count}"
