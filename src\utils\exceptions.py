"""
Custom exception classes for better error handling
"""


class StatArbError(Exception):
    """Base exception for multi-strategy trading system errors"""
    pass


class ConfigurationError(StatArbError):
    """Raised when configuration is invalid"""
    pass


class ExchangeConnectionError(StatArbError):
    """Raised when exchange connection fails"""
    pass


class DataFetchError(StatArbError):
    """Raised when data fetching fails"""
    pass


class OrderExecutionError(StatArbError):
    """Raised when order execution fails"""
    pass


class PositionManagementError(StatArbError):
    """Raised when position management fails"""
    pass


class ValidationError(StatArbError):
    """Raised when data validation fails"""
    pass


class InsufficientDataError(DataFetchError):
    """Raised when insufficient data is available"""
    pass


class ContractSpecificationError(ValidationError):
    """Raised when contract specification validation fails"""
    pass


class CredentialError(ConfigurationError):
    """Raised when credentials are missing or invalid"""
    pass


class RiskManagementError(StatArbError):
    """Raised when risk management rules are violated"""
    pass


# ============================================================================
# API and Network Specific Exceptions
# ============================================================================

class APIError(StatArbError):
    """Base class for API-related errors"""
    def __init__(self, message: str, exchange: str = None, symbol: str = None, retry_after: int = None):
        super().__init__(message)
        self.exchange = exchange
        self.symbol = symbol
        self.retry_after = retry_after


class RateLimitError(APIError):
    """Raised when API rate limits are exceeded"""
    def __init__(self, message: str, exchange: str = None, symbol: str = None, retry_after: int = None):
        super().__init__(message, exchange, symbol, retry_after)


class NetworkError(APIError):
    """Raised when network connectivity issues occur"""
    def __init__(self, message: str, exchange: str = None, symbol: str = None, is_timeout: bool = False):
        super().__init__(message, exchange, symbol)
        self.is_timeout = is_timeout


class ExchangeMaintenanceError(APIError):
    """Raised when exchange is under maintenance"""
    def __init__(self, message: str, exchange: str = None, estimated_duration: int = None):
        super().__init__(message, exchange)
        self.estimated_duration = estimated_duration


class InsufficientBalanceError(APIError):
    """Raised when account has insufficient balance for operation"""
    pass


class InvalidOrderError(APIError):
    """Raised when order parameters are invalid"""
    pass


class MarketClosedError(APIError):
    """Raised when market is closed for trading"""
    pass


class CircuitBreakerError(StatArbError):
    """Raised when circuit breaker is open"""
    def __init__(self, message: str, retry_after: int = None):
        super().__init__(message)
        self.retry_after = retry_after
