"""
Binance exchange implementation with comprehensive error handling
"""

import ccxt.async_support as ccxt
import logging
from typing import Dict, List, Optional, Any
from exchanges.base import ExchangeInterface

logger = logging.getLogger(__name__)


class BinanceExchange(ExchangeInterface):
    """Binance exchange implementation with comprehensive error handling"""

    def __init__(self):
        self.exchange = None
        self._name = "Binance"

    @property
    def name(self) -> str:
        return self._name

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize Binance exchange connection with error handling"""
        try:
            api_key = config.get('api_key', '').strip()
            api_secret = config.get('api_secret', '').strip()

            constructor_params = {
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use futures for perpetual contracts
                },
            }

            # Add API keys if provided
            if api_key and api_secret:
                constructor_params['apiKey'] = api_key
                constructor_params['secret'] = api_secret
                logger.info("🔑 Using API keys for private endpoints")
            else:
                logger.info("🌐 Using public endpoints only (no API keys provided)")

            self.exchange = ccxt.binance(constructor_params)

            # Enable sandbox mode if using testnet (but not for demo)
            if config.get('use_testnet', False) and not config.get('use_demo', False):
                self.exchange.set_sandbox_mode(True)
            elif config.get('use_demo', False):
                logger.info("🎭 Binance demo trading mode enabled")

            markets = await self.fetch_markets()

            # Determine mode for logging
            if config.get('use_demo', False):
                mode = "demo"
            elif config.get('use_testnet', False):
                mode = "testnet"
            else:
                mode = "live"
            logger.info(f"✅ Connected to {self.name} {mode}")
            logger.info(f"✅ Found {len(markets)} markets")

            # Validate API key if provided
            if api_key and api_secret:
                try:
                    balance = await self.fetch_balance()
                    logger.info("✅ API keys are valid and working")
                    logger.info(f"✅ {mode.title()} account balance available: {len(balance)} currencies")
                except Exception as api_error:
                    logger.warning(f"⚠️ API key test failed: {api_error}")
                    logger.warning("⚠️ Will run in public data mode only")
                    self.exchange.apiKey = None
                    self.exchange.secret = None

            return True

        except Exception as e:
            logger.error(f"❌ Failed to connect to {self.name}: {e}")
            return False

    async def fetch_markets(self) -> Dict[str, Any]:
        """Fetch available markets"""
        try:
            return await self.exchange.load_markets()
        except Exception as e:
            logger.error(f"❌ Failed to fetch markets from {self.name}: {e}")
            raise

    async def fetch_tickers(self) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_tickers()
        except Exception as e:
            logger.error(f"❌ Failed to fetch tickers from {self.name}: {e}")
            raise

    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_ticker(symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch ticker for {symbol} from {self.name}: {e}")
            raise

    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List[List]:
        try:
            return await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        except Exception as e:
            logger.error(f"❌ Failed to fetch OHLCV for {symbol} from {self.name}: {e}")
            raise

    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_funding_rate(symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate for {symbol} from {self.name}: {e}")
            raise

    async def fetch_funding_rate_history(self, symbol: str, limit: int) -> List[Dict]:
        try:
            return await self.exchange.fetch_funding_rate_history(symbol, limit=limit)
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate history for {symbol} from {self.name}: {e}")
            raise

    async def fetch_balance(self) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_balance()
        except Exception as e:
            logger.error(f"❌ Failed to fetch balance from {self.name}: {e}")
            raise

    async def fetch_positions(self, symbols: Optional[List[str]] = None) -> List[Dict]:
        try:
            if symbols:
                return await self.exchange.fetch_positions(symbols)
            else:
                return await self.exchange.fetch_positions()
        except Exception as e:
            logger.error(f"❌ Failed to fetch positions from {self.name}: {e}")
            raise

    async def fetch_open_orders(self, symbol: str) -> List[Dict]:
        try:
            return await self.exchange.fetch_open_orders(symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch open orders for {symbol} from {self.name}: {e}")
            raise

    async def fetch_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_order(order_id, symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch order {order_id} for {symbol} from {self.name}: {e}")
            raise

    async def fetch_order_book(self, symbol: str) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_order_book(symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch order book for {symbol} from {self.name}: {e}")
            raise

    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict[str, Any]:
        try:
            return await self.exchange.create_limit_order(symbol, side, amount, price, params)
        except Exception as e:
            logger.error(f"❌ Failed to create limit order for {symbol} on {self.name}: {e}")
            raise

    async def create_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict[str, Any]:
        try:
            return await self.exchange.create_market_order(symbol, side, amount, params)
        except Exception as e:
            logger.error(f"❌ Failed to create market order for {symbol} on {self.name}: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        try:
            return await self.exchange.cancel_order(order_id, symbol)
        except Exception as e:
            logger.error(f"❌ Failed to cancel order {order_id} for {symbol} on {self.name}: {e}")
            raise
