#!/usr/bin/env python3

"""
Beta Projection Optimizer

Implements constrained optimization to achieve beta-neutral portfolios while minimizing
weight changes from the original volatility-targeted weights.

Key Features:
- Constrained optimization using scipy.optimize
- Beta neutrality constraint (portfolio beta = 0)
- Weight normalization constraint (sum of weights = 1)
- Minimizes weight changes from original allocation
- Handles optimization failures gracefully
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from scipy.optimize import minimize
import warnings

from utils import get_logger

logger = get_logger(__name__)


class BetaOptimizer:
    """
    Optimize portfolio weights to achieve beta neutrality while minimizing changes
    from the original volatility-targeted weights.
    
    This solves the constrained optimization problem:
    
    Minimize: ||w_new - w_original||²
    Subject to:
        1. Σ(w_i × beta_i) = 0  (beta neutrality)
        2. Σ(w_i) = 1           (weight normalization)
        3. |w_new_i - w_original_i| ≤ max_change  (optional weight change limits)
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.beta_neutrality_tolerance = config.get('beta_neutrality_tolerance', 0.05)
        self.max_weight_change = config.get('beta_optimization_max_weight_change', 0.20)
        
        logger.info(f"🔧 Beta Optimizer initialized")
        logger.info(f"🔧 Beta neutrality tolerance: ±{self.beta_neutrality_tolerance:.3f}")
        logger.info(f"🔧 Maximum weight change: ±{self.max_weight_change:.3f}")
    
    def optimize_weights_for_beta_neutrality(self, positions: List[Dict], betas: List[float]) -> Optional[List[Dict]]:
        """
        Optimize position weights to achieve beta neutrality.
        
        Args:
            positions: List of position dictionaries with 'symbol', 'weight', etc.
            betas: List of beta values corresponding to each position
            
        Returns:
            List of optimized position dictionaries or None if optimization fails
        """
        try:
            if not positions or not betas or len(positions) != len(betas):
                logger.warning("⚠️ Invalid input for beta optimization")
                return None
            
            # Extract original weights and symbols
            original_weights = np.array([pos.get('weight', 0.0) for pos in positions])
            symbols = [pos.get('symbol') for pos in positions]
            betas_array = np.array(betas)
            
            # Check if we already have beta neutrality
            current_portfolio_beta = np.sum(original_weights * betas_array)
            if abs(current_portfolio_beta) <= self.beta_neutrality_tolerance:
                logger.info(f"✅ Portfolio already beta-neutral: {current_portfolio_beta:.4f}")
                return positions
            
            logger.info(f"📊 Optimizing weights for beta neutrality...")
            logger.info(f"📊 Current portfolio beta: {current_portfolio_beta:.4f}")
            logger.info(f"📊 Target beta: 0.0 ± {self.beta_neutrality_tolerance:.3f}")
            
            # Perform constrained optimization
            optimized_weights = self._solve_optimization_problem(original_weights, betas_array)
            
            if optimized_weights is None:
                logger.warning("⚠️ Beta optimization failed, using original weights")
                return positions
            
            # Validate optimization results
            if not self._validate_optimization_results(optimized_weights, betas_array, original_weights):
                logger.warning("⚠️ Optimization results failed validation, using original weights")
                return positions
            
            # Create optimized positions
            optimized_positions = self._create_optimized_positions(positions, optimized_weights)
            
            # Log optimization results
            self._log_optimization_results(original_weights, optimized_weights, betas_array, symbols)
            
            return optimized_positions
            
        except Exception as e:
            logger.error(f"❌ Error in beta optimization: {e}")
            return None
    
    def _solve_optimization_problem(self, original_weights: np.ndarray, betas: np.ndarray) -> Optional[np.ndarray]:
        """
        Solve the constrained optimization problem using scipy.optimize.
        
        Args:
            original_weights: Original weight vector
            betas: Beta vector for each asset
            
        Returns:
            Optimized weight vector or None if optimization fails
        """
        try:
            n_assets = len(original_weights)
            
            # Objective function: minimize squared weight changes
            def objective(w):
                return np.sum((w - original_weights) ** 2)
            
            # Constraints
            constraints = [
                # Beta neutrality constraint: Σ(w_i × beta_i) = 0
                {
                    'type': 'eq',
                    'fun': lambda w: np.sum(w * betas)
                },
                # Weight normalization constraint: Σ(w_i) = 1
                {
                    'type': 'eq',
                    'fun': lambda w: np.sum(w) - 1.0
                }
            ]
            
            # Bounds: limit weight changes
            bounds = []
            for i, orig_weight in enumerate(original_weights):
                lower_bound = max(0.0, orig_weight - self.max_weight_change)
                upper_bound = min(1.0, orig_weight + self.max_weight_change)
                bounds.append((lower_bound, upper_bound))
            
            # Initial guess: start with original weights
            x0 = original_weights.copy()
            
            # Suppress optimization warnings for cleaner logs
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                # Solve optimization problem
                result = minimize(
                    objective,
                    x0,
                    method='SLSQP',  # Sequential Least Squares Programming
                    bounds=bounds,
                    constraints=constraints,
                    options={
                        'maxiter': 1000,
                        'ftol': 1e-9,
                        'disp': False
                    }
                )
            
            if not result.success:
                logger.warning(f"⚠️ Optimization failed: {result.message}")
                return None
            
            optimized_weights = result.x
            
            # Ensure weights are non-negative and normalized
            optimized_weights = np.maximum(optimized_weights, 0.0)
            weight_sum = np.sum(optimized_weights)
            if weight_sum > 0:
                optimized_weights = optimized_weights / weight_sum
            else:
                logger.warning("⚠️ All optimized weights are zero")
                return None
            
            return optimized_weights
            
        except Exception as e:
            logger.error(f"❌ Error solving optimization problem: {e}")
            return None
    
    def _validate_optimization_results(self, optimized_weights: np.ndarray, betas: np.ndarray, 
                                     original_weights: np.ndarray) -> bool:
        """
        Validate that optimization results meet our constraints and expectations.
        
        Args:
            optimized_weights: Optimized weight vector
            betas: Beta vector
            original_weights: Original weight vector
            
        Returns:
            True if validation passes, False otherwise
        """
        try:
            # Check weight normalization
            weight_sum = np.sum(optimized_weights)
            if abs(weight_sum - 1.0) > 1e-6:
                logger.warning(f"⚠️ Weights don't sum to 1: {weight_sum:.6f}")
                return False
            
            # Check beta neutrality
            portfolio_beta = np.sum(optimized_weights * betas)
            if abs(portfolio_beta) > self.beta_neutrality_tolerance:
                logger.warning(f"⚠️ Portfolio not beta-neutral: {portfolio_beta:.4f}")
                return False
            
            # Check weight change limits
            weight_changes = np.abs(optimized_weights - original_weights)
            max_change = np.max(weight_changes)
            if max_change > self.max_weight_change + 1e-6:  # Small tolerance for numerical precision
                logger.warning(f"⚠️ Weight change exceeds limit: {max_change:.4f} > {self.max_weight_change:.4f}")
                return False
            
            # Check for negative weights
            if np.any(optimized_weights < -1e-6):
                logger.warning(f"⚠️ Negative weights detected: {np.min(optimized_weights):.6f}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error validating optimization results: {e}")
            return False
    
    def _create_optimized_positions(self, original_positions: List[Dict], 
                                  optimized_weights: np.ndarray) -> List[Dict]:
        """
        Create new position dictionaries with optimized weights.
        
        Args:
            original_positions: Original position list
            optimized_weights: Optimized weight vector
            
        Returns:
            List of position dictionaries with updated weights
        """
        optimized_positions = []
        
        for i, position in enumerate(original_positions):
            # Create a copy of the original position
            optimized_position = position.copy()
            
            # Update the weight
            optimized_position['weight'] = float(optimized_weights[i])
            optimized_position['original_weight'] = position.get('weight', 0.0)
            optimized_position['weight_change'] = float(optimized_weights[i]) - position.get('weight', 0.0)
            optimized_position['beta_optimized'] = True
            
            optimized_positions.append(optimized_position)
        
        return optimized_positions
    
    def _log_optimization_results(self, original_weights: np.ndarray, optimized_weights: np.ndarray,
                                betas: np.ndarray, symbols: List[str]) -> None:
        """
        Log detailed optimization results for analysis.
        
        Args:
            original_weights: Original weight vector
            optimized_weights: Optimized weight vector
            betas: Beta vector
            symbols: List of symbols
        """
        try:
            # Calculate portfolio betas
            original_portfolio_beta = np.sum(original_weights * betas)
            optimized_portfolio_beta = np.sum(optimized_weights * betas)
            
            # Calculate weight changes
            weight_changes = optimized_weights - original_weights
            total_weight_change = np.sum(np.abs(weight_changes))
            max_weight_change = np.max(np.abs(weight_changes))
            
            logger.info(f"✅ Beta optimization completed successfully")
            logger.info(f"📊 Portfolio beta: {original_portfolio_beta:.4f} → {optimized_portfolio_beta:.4f}")
            logger.info(f"📊 Total weight change: {total_weight_change:.4f}")
            logger.info(f"📊 Maximum weight change: {max_weight_change:.4f}")
            
            # Log individual position changes (only significant changes)
            logger.info(f"📊 Significant weight changes:")
            for i, symbol in enumerate(symbols):
                change = weight_changes[i]
                if abs(change) > 0.01:  # Only log changes > 1%
                    logger.info(f"   {symbol}: {original_weights[i]:.4f} → {optimized_weights[i]:.4f} "
                              f"(Δ{change:+.4f}, β={betas[i]:.4f})")
            
        except Exception as e:
            logger.error(f"❌ Error logging optimization results: {e}")
