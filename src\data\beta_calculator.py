#!/usr/bin/env python3

"""
Beta Calculator Module

Calculates rolling beta for crypto assets against a market index (typically BTC).
Beta measures the sensitivity of an asset's returns to market returns.

Key Features:
- 60-day rolling beta calculation
- Caching for performance optimization
- Robust error handling for missing data
- Market index data fetching and caching
"""

import asyncio
import statistics
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from decimal import Decimal

from utils import get_logger
from data.cache import C<PERSON><PERSON>ey

logger = get_logger(__name__)


class BetaCalculator:
    """
    Calculate rolling beta for crypto assets against a market index.
    
    Beta = Covariance(asset_returns, market_returns) / Variance(market_returns)
    
    A beta of:
    - 1.0 means the asset moves with the market
    - > 1.0 means the asset is more volatile than the market
    - < 1.0 means the asset is less volatile than the market
    - 0.0 means no correlation with the market
    """
    
    def __init__(self, data_fetcher, config: Dict[str, Any]):
        self.data_fetcher = data_fetcher
        self.config = config
        self.market_index_symbol = config.get('market_index_symbol', 'BTCUSDT')
        self.beta_calculation_days = config.get('beta_calculation_days', 60)
        self.default_beta = config.get('default_beta', 1.0)
        
        logger.info(f"🔧 Beta Calculator initialized with market index: {self.market_index_symbol}")
        logger.info(f"🔧 Beta calculation period: {self.beta_calculation_days} days")
    
    async def calculate_beta(self, symbol: str) -> Optional[float]:
        """
        Calculate 60-day rolling beta for a symbol against the market index.
        
        Args:
            symbol: Trading symbol (e.g., 'ETHUSDT')
            
        Returns:
            Beta value or None if calculation fails
        """
        try:
            # Check cache first
            cache_key = CacheKey.beta(symbol, self.beta_calculation_days)
            cached_beta = self.data_fetcher.cache.get(cache_key)
            if cached_beta is not None:
                logger.debug(f"📋 Using cached beta for {symbol}: {cached_beta:.4f}")
                return cached_beta
            
            # Skip beta calculation for the market index itself
            if symbol == self.market_index_symbol:
                beta = 1.0  # Market index has beta of 1.0 by definition
                self.data_fetcher.cache.set(cache_key, beta, ttl=self.config.get('beta_cache_ttl', 3600))
                logger.debug(f"📊 {symbol} is market index, beta = 1.0")
                return beta
            
            # Fetch OHLCV data for both asset and market index
            asset_data, market_data = await asyncio.gather(
                self.data_fetcher.get_cached_ohlcv(symbol, '1d', self.beta_calculation_days + 1),
                self.data_fetcher.get_cached_ohlcv(self.market_index_symbol, '1d', self.beta_calculation_days + 1),
                return_exceptions=True
            )
            
            # Handle fetch errors
            if isinstance(asset_data, Exception):
                logger.warning(f"⚠️ Failed to fetch asset data for {symbol}: {asset_data}")
                return None
            if isinstance(market_data, Exception):
                logger.warning(f"⚠️ Failed to fetch market data for {self.market_index_symbol}: {market_data}")
                return None
            
            # Validate data availability
            if not asset_data or len(asset_data) < self.beta_calculation_days + 1:
                logger.debug(f"⚠️ Insufficient asset data for {symbol} beta calculation")
                return None
            if not market_data or len(market_data) < self.beta_calculation_days + 1:
                logger.debug(f"⚠️ Insufficient market data for {self.market_index_symbol} beta calculation")
                return None
            
            # Calculate returns for both asset and market
            asset_returns = self._calculate_returns(asset_data)
            market_returns = self._calculate_returns(market_data)
            
            if not asset_returns or not market_returns:
                logger.debug(f"⚠️ Failed to calculate returns for {symbol} beta calculation")
                return None
            
            # Ensure we have the same number of returns (handle potential data misalignment)
            min_length = min(len(asset_returns), len(market_returns))
            if min_length < self.beta_calculation_days:
                logger.debug(f"⚠️ Insufficient aligned returns for {symbol} beta calculation ({min_length} < {self.beta_calculation_days})")
                return None
            
            # Take the last N returns to ensure alignment
            asset_returns = asset_returns[-min_length:]
            market_returns = market_returns[-min_length:]
            
            # Calculate beta using numpy for numerical stability
            beta = self._calculate_beta_from_returns(asset_returns, market_returns)
            
            if beta is None:
                logger.debug(f"⚠️ Beta calculation failed for {symbol}")
                return None
            
            # Cache the result
            cache_ttl = self.config.get('beta_cache_ttl', 3600)
            self.data_fetcher.cache.set(cache_key, beta, ttl=cache_ttl)
            
            logger.debug(f"📊 {symbol} beta: {beta:.4f} (vs {self.market_index_symbol})")
            return beta
            
        except Exception as e:
            logger.error(f"❌ Error calculating beta for {symbol}: {e}")
            return None
    
    def _calculate_returns(self, ohlcv_data: List[List]) -> Optional[List[float]]:
        """
        Calculate daily returns from OHLCV data.
        
        Args:
            ohlcv_data: OHLCV data [[timestamp, open, high, low, close, volume], ...]
            
        Returns:
            List of daily returns or None if calculation fails
        """
        try:
            if len(ohlcv_data) < 2:
                return None
            
            # Extract closing prices
            closes = [float(candle[4]) for candle in ohlcv_data]
            
            # Calculate daily returns with division by zero protection
            returns = []
            for i in range(1, len(closes)):
                if closes[i-1] == 0:
                    logger.warning(f"⚠️ Zero price detected in beta calculation, skipping")
                    continue
                daily_return = (closes[i] - closes[i-1]) / closes[i-1]
                returns.append(daily_return)
            
            return returns if len(returns) >= self.beta_calculation_days else None
            
        except Exception as e:
            logger.debug(f"❌ Error calculating returns: {e}")
            return None
    
    def _calculate_beta_from_returns(self, asset_returns: List[float], market_returns: List[float]) -> Optional[float]:
        """
        Calculate beta from asset and market returns using covariance/variance formula.
        
        Beta = Covariance(asset_returns, market_returns) / Variance(market_returns)
        
        Args:
            asset_returns: List of asset daily returns
            market_returns: List of market daily returns
            
        Returns:
            Beta value or None if calculation fails
        """
        try:
            if len(asset_returns) != len(market_returns) or len(asset_returns) < 2:
                return None
            
            # Convert to numpy arrays for numerical stability
            asset_array = np.array(asset_returns)
            market_array = np.array(market_returns)
            
            # Calculate variance of market returns
            market_variance = np.var(market_array, ddof=1)  # Use sample variance (ddof=1)
            
            # Handle zero variance (market didn't move)
            if market_variance == 0 or np.isnan(market_variance):
                logger.debug(f"⚠️ Market variance is zero or NaN, cannot calculate beta")
                return self.default_beta
            
            # Calculate covariance between asset and market returns
            covariance = np.cov(asset_array, market_array, ddof=1)[0, 1]  # Use sample covariance
            
            # Handle NaN covariance
            if np.isnan(covariance):
                logger.debug(f"⚠️ Covariance is NaN, using default beta")
                return self.default_beta
            
            # Calculate beta
            beta = covariance / market_variance
            
            # Handle NaN or infinite beta
            if np.isnan(beta) or np.isinf(beta):
                logger.debug(f"⚠️ Beta is NaN or infinite, using default beta")
                return self.default_beta
            
            # Sanity check: extremely high or low beta values might indicate data issues
            if abs(beta) > 10:
                logger.warning(f"⚠️ Extreme beta value detected: {beta:.4f}, capping at ±10")
                beta = 10.0 if beta > 0 else -10.0
            
            return float(beta)
            
        except Exception as e:
            logger.debug(f"❌ Error calculating beta from returns: {e}")
            return None
    
    async def calculate_portfolio_beta(self, positions: List[Dict]) -> float:
        """
        Calculate portfolio-level beta as weighted average of individual asset betas.
        
        Portfolio Beta = Σ(weight_i × beta_i)
        
        Args:
            positions: List of position dictionaries with 'symbol' and 'weight' keys
            
        Returns:
            Portfolio beta value
        """
        try:
            if not positions:
                return 0.0
            
            total_weighted_beta = 0.0
            total_weight = 0.0
            
            for position in positions:
                symbol = position.get('symbol')
                weight = position.get('weight', 0.0)
                
                if not symbol or weight == 0:
                    continue
                
                # Get beta for this asset
                beta = await self.calculate_beta(symbol)
                if beta is None:
                    beta = self.default_beta
                    logger.debug(f"⚠️ Using default beta {beta} for {symbol}")
                
                # Add to weighted sum
                total_weighted_beta += weight * beta
                total_weight += abs(weight)  # Use absolute weight for proper normalization
                
                logger.debug(f"📊 {symbol}: weight={weight:.4f}, beta={beta:.4f}, contribution={weight*beta:.4f}")
            
            # Calculate portfolio beta
            if total_weight == 0:
                portfolio_beta = 0.0
            else:
                portfolio_beta = total_weighted_beta / total_weight
            
            logger.info(f"📊 Portfolio beta: {portfolio_beta:.4f} (total weight: {total_weight:.4f})")
            return portfolio_beta
            
        except Exception as e:
            logger.error(f"❌ Error calculating portfolio beta: {e}")
            return 0.0


# Add beta cache key to the CacheKey class
def _add_beta_cache_key():
    """Add beta cache key method to CacheKey class"""
    def beta(symbol: str, days: int) -> str:
        """Generate cache key for beta data"""
        return f"beta:{symbol}:{days}d"
    
    # Add the method to CacheKey class
    CacheKey.beta = staticmethod(beta)

# Initialize the cache key extension
_add_beta_cache_key()
