"""
Portfolio combination logic for netting positions from multiple strategies

This module handles the combination of target portfolios from multiple strategies,
applying strategy weights and netting positions by symbol.
"""

import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict

from strategies.base import StrategyResult, StrategyPosition

logger = logging.getLogger(__name__)


@dataclass
class CombinedPosition:
    """Represents a position after combining multiple strategy positions"""
    symbol: str
    side: str  # 'long' or 'short'
    size_usd: float
    size_native: float
    net_weight: float  # Combined weight from all strategies
    contributing_strategies: List[str] = field(default_factory=list)
    strategy_contributions: Dict[str, float] = field(default_factory=dict)  # Strategy -> USD contribution
    confidence: float = 1.0  # Combined confidence score
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate combined position data"""
        if self.side not in ['long', 'short']:
            raise ValueError(f"Invalid side: {self.side}. Must be 'long' or 'short'")
        if self.size_usd < 0:
            raise ValueError(f"Invalid size_usd: {self.size_usd}. Must be non-negative")


class PortfolioCombiner:
    """
    Combines target portfolios from multiple strategies into a single portfolio
    
    The combiner handles:
    - Applying strategy weights to individual positions
    - Netting long/short positions by symbol
    - Maintaining attribution to source strategies
    - Handling position conflicts and edge cases
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize portfolio combiner
        
        Args:
            config: Configuration containing combination settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Configuration parameters
        self.min_position_size_usd = config.get('min_position_size_usd', 10.0)
        self.position_rounding_decimals = config.get('position_rounding_decimals', 6)
        self.enable_position_netting = config.get('enable_position_netting', True)
        
        self.logger.info(f"🏗️ Portfolio combiner initialized")
        self.logger.info(f"   Min position size: ${self.min_position_size_usd}")
        self.logger.info(f"   Position netting: {'enabled' if self.enable_position_netting else 'disabled'}")
    
    def combine_portfolios(self, strategy_results: Dict[str, StrategyResult], 
                          strategy_weights: Dict[str, float]) -> List[CombinedPosition]:
        """
        Combine multiple strategy portfolios into a single target portfolio
        
        Args:
            strategy_results: Dictionary mapping strategy names to their results
            strategy_weights: Dictionary mapping strategy names to their portfolio weights
            
        Returns:
            List of CombinedPosition objects representing the final portfolio
        """
        if not strategy_results:
            self.logger.warning("⚠️ No strategy results provided for combination")
            return []
        
        # Filter successful strategies only
        successful_results = {
            name: result for name, result in strategy_results.items() 
            if result.success and result.target_positions
        }
        
        if not successful_results:
            self.logger.warning("⚠️ No successful strategies with positions to combine")
            return []
        
        self.logger.info(f"🔄 Combining portfolios from {len(successful_results)} successful strategies...")
        
        # Normalize strategy weights for successful strategies only
        normalized_weights = self._normalize_weights(successful_results.keys(), strategy_weights)
        
        # Apply strategy weights to positions
        weighted_positions = self._apply_strategy_weights(successful_results, normalized_weights)
        
        # Net positions by symbol if enabled
        if self.enable_position_netting:
            combined_positions = self._net_positions_by_symbol(weighted_positions)
        else:
            combined_positions = self._combine_positions_without_netting(weighted_positions)
        
        # Filter out positions below minimum size
        final_positions = self._filter_minimum_positions(combined_positions)
        
        # Log combination summary
        self._log_combination_summary(strategy_results, final_positions, normalized_weights)
        
        return final_positions
    
    def _normalize_weights(self, strategy_names: List[str], 
                          strategy_weights: Dict[str, float]) -> Dict[str, float]:
        """
        Normalize strategy weights to sum to 1.0 for successful strategies only
        
        Args:
            strategy_names: Names of successful strategies
            strategy_weights: Original strategy weights
            
        Returns:
            Normalized weights dictionary
        """
        # Get weights for successful strategies only
        relevant_weights = {
            name: strategy_weights.get(name, 1.0) 
            for name in strategy_names
        }
        
        total_weight = sum(relevant_weights.values())
        
        if total_weight == 0:
            # Equal weights if all weights are zero
            normalized = {name: 1.0 / len(strategy_names) for name in strategy_names}
        else:
            normalized = {name: weight / total_weight for name, weight in relevant_weights.items()}
        
        self.logger.info(f"📊 Normalized strategy weights: {normalized}")
        return normalized
    
    def _apply_strategy_weights(self, strategy_results: Dict[str, StrategyResult],
                               normalized_weights: Dict[str, float]) -> List[Tuple[str, StrategyPosition]]:
        """
        Apply strategy weights to all positions
        
        Args:
            strategy_results: Successful strategy results
            normalized_weights: Normalized strategy weights
            
        Returns:
            List of (strategy_name, weighted_position) tuples
        """
        weighted_positions = []
        
        for strategy_name, result in strategy_results.items():
            strategy_weight = normalized_weights.get(strategy_name, 0.0)
            
            if strategy_weight == 0:
                self.logger.warning(f"⚠️ Strategy {strategy_name} has zero weight, skipping positions")
                continue
            
            for position in result.target_positions:
                # Create weighted copy of position
                weighted_position = StrategyPosition(
                    symbol=position.symbol,
                    side=position.side,
                    size_usd=position.size_usd * strategy_weight,
                    size_native=position.size_native * strategy_weight,
                    weight=position.weight * strategy_weight,
                    confidence=position.confidence,
                    metadata=position.metadata.copy()
                )
                
                # Add strategy attribution
                weighted_position.metadata['source_strategy'] = strategy_name
                weighted_position.metadata['strategy_weight'] = strategy_weight
                weighted_position.metadata['original_size_usd'] = position.size_usd
                
                weighted_positions.append((strategy_name, weighted_position))
        
        self.logger.info(f"📊 Applied weights to {len(weighted_positions)} positions")
        return weighted_positions
    
    def _net_positions_by_symbol(self, weighted_positions: List[Tuple[str, StrategyPosition]]) -> List[CombinedPosition]:
        """
        Net positions by symbol, combining long and short positions
        
        Args:
            weighted_positions: List of (strategy_name, weighted_position) tuples
            
        Returns:
            List of netted CombinedPosition objects
        """
        # Group positions by symbol
        symbol_positions = defaultdict(list)
        for strategy_name, position in weighted_positions:
            symbol_positions[position.symbol].append((strategy_name, position))
        
        combined_positions = []
        
        for symbol, positions in symbol_positions.items():
            # Separate long and short positions
            long_positions = [(s, p) for s, p in positions if p.side == 'long']
            short_positions = [(s, p) for s, p in positions if p.side == 'short']
            
            # Calculate net position
            long_total_usd = sum(p.size_usd for _, p in long_positions)
            short_total_usd = sum(p.size_usd for _, p in short_positions)
            
            net_usd = long_total_usd - short_total_usd
            
            if abs(net_usd) < self.min_position_size_usd:
                self.logger.debug(f"⏭️ {symbol}: Net position ${abs(net_usd):.2f} below minimum, skipping")
                continue
            
            # Determine final side and size
            final_side = 'long' if net_usd > 0 else 'short'
            final_size_usd = abs(net_usd)
            
            # Calculate weighted average native size
            if final_side == 'long' and long_positions:
                # Use long positions for native size calculation
                total_weight = sum(p.size_usd for _, p in long_positions)
                final_size_native = sum(p.size_native * (p.size_usd / total_weight) for _, p in long_positions)
            elif final_side == 'short' and short_positions:
                # Use short positions for native size calculation  
                total_weight = sum(p.size_usd for _, p in short_positions)
                final_size_native = sum(p.size_native * (p.size_usd / total_weight) for _, p in short_positions)
            else:
                # Fallback: estimate from USD size using first available position
                reference_position = positions[0][1]
                price_estimate = reference_position.size_usd / reference_position.size_native if reference_position.size_native > 0 else 1.0
                final_size_native = final_size_usd / price_estimate
            
            # Round native size
            final_size_native = round(final_size_native, self.position_rounding_decimals)
            
            # Collect strategy contributions and metadata
            contributing_strategies = list(set(s for s, _ in positions))
            strategy_contributions = {}
            combined_confidence = 0.0
            total_contribution_weight = 0.0
            
            for strategy_name, position in positions:
                contribution = position.size_usd if position.side == final_side else -position.size_usd
                if final_side == 'short':
                    contribution = -contribution  # Adjust for short positions
                
                strategy_contributions[strategy_name] = contribution
                
                # Weight confidence by position size
                combined_confidence += position.confidence * position.size_usd
                total_contribution_weight += position.size_usd
            
            # Calculate weighted average confidence
            if total_contribution_weight > 0:
                combined_confidence /= total_contribution_weight
            else:
                combined_confidence = 1.0
            
            # Create combined position
            combined_position = CombinedPosition(
                symbol=symbol,
                side=final_side,
                size_usd=final_size_usd,
                size_native=final_size_native,
                net_weight=final_size_usd,  # Will be normalized later if needed
                contributing_strategies=contributing_strategies,
                strategy_contributions=strategy_contributions,
                confidence=combined_confidence,
                metadata={
                    'long_total_usd': long_total_usd,
                    'short_total_usd': short_total_usd,
                    'net_usd': net_usd,
                    'position_count': len(positions)
                }
            )
            
            combined_positions.append(combined_position)
            
            self.logger.debug(f"📊 {symbol}: Combined {len(positions)} positions -> "
                            f"{final_side} ${final_size_usd:.2f} "
                            f"(long: ${long_total_usd:.2f}, short: ${short_total_usd:.2f})")
        
        self.logger.info(f"✅ Netted {len(symbol_positions)} symbols into {len(combined_positions)} final positions")
        return combined_positions

    def _combine_positions_without_netting(self, weighted_positions: List[Tuple[str, StrategyPosition]]) -> List[CombinedPosition]:
        """
        Combine positions without netting (keep all positions separate)

        Args:
            weighted_positions: List of (strategy_name, weighted_position) tuples

        Returns:
            List of CombinedPosition objects (one per input position)
        """
        combined_positions = []

        for strategy_name, position in weighted_positions:
            if position.size_usd < self.min_position_size_usd:
                self.logger.debug(f"⏭️ {strategy_name}:{position.symbol}: Position ${position.size_usd:.2f} below minimum, skipping")
                continue

            combined_position = CombinedPosition(
                symbol=position.symbol,
                side=position.side,
                size_usd=position.size_usd,
                size_native=position.size_native,
                net_weight=position.weight,
                contributing_strategies=[strategy_name],
                strategy_contributions={strategy_name: position.size_usd},
                confidence=position.confidence,
                metadata=position.metadata.copy()
            )

            combined_positions.append(combined_position)

        self.logger.info(f"✅ Combined {len(weighted_positions)} positions without netting into {len(combined_positions)} final positions")
        return combined_positions

    def _filter_minimum_positions(self, positions: List[CombinedPosition]) -> List[CombinedPosition]:
        """
        Filter out positions below minimum size threshold

        Args:
            positions: List of combined positions

        Returns:
            Filtered list of positions
        """
        filtered_positions = [
            pos for pos in positions
            if pos.size_usd >= self.min_position_size_usd
        ]

        filtered_count = len(positions) - len(filtered_positions)
        if filtered_count > 0:
            self.logger.info(f"🔍 Filtered out {filtered_count} positions below ${self.min_position_size_usd} minimum")

        return filtered_positions

    def _log_combination_summary(self, strategy_results: Dict[str, StrategyResult],
                                final_positions: List[CombinedPosition],
                                normalized_weights: Dict[str, float]) -> None:
        """Log detailed combination summary"""

        # Calculate totals
        total_input_positions = sum(len(r.target_positions) for r in strategy_results.values() if r.success)
        total_final_positions = len(final_positions)
        total_final_capital = sum(pos.size_usd for pos in final_positions)

        long_positions = [pos for pos in final_positions if pos.side == 'long']
        short_positions = [pos for pos in final_positions if pos.side == 'short']

        long_capital = sum(pos.size_usd for pos in long_positions)
        short_capital = sum(pos.size_usd for pos in short_positions)

        self.logger.info(f"📊 Portfolio Combination Summary:")
        self.logger.info(f"   Input: {total_input_positions} positions from {len(strategy_results)} strategies")
        self.logger.info(f"   Output: {total_final_positions} final positions (${total_final_capital:,.0f})")
        self.logger.info(f"   Long: {len(long_positions)} positions (${long_capital:,.0f})")
        self.logger.info(f"   Short: {len(short_positions)} positions (${short_capital:,.0f})")
        self.logger.info(f"   Strategy weights: {normalized_weights}")

        # Log per-strategy contribution
        for strategy_name, weight in normalized_weights.items():
            strategy_positions = [
                pos for pos in final_positions
                if strategy_name in pos.contributing_strategies
            ]
            strategy_capital = sum(
                pos.strategy_contributions.get(strategy_name, 0)
                for pos in final_positions
            )

            self.logger.info(f"   {strategy_name}: {len(strategy_positions)} positions, "
                           f"${abs(strategy_capital):,.0f} contribution (weight: {weight:.3f})")

    def get_portfolio_statistics(self, positions: List[CombinedPosition]) -> Dict[str, Any]:
        """
        Calculate portfolio statistics for the combined positions

        Args:
            positions: List of combined positions

        Returns:
            Dictionary containing portfolio statistics
        """
        if not positions:
            return {
                'total_positions': 0,
                'total_capital': 0.0,
                'long_positions': 0,
                'short_positions': 0,
                'long_capital': 0.0,
                'short_capital': 0.0,
                'symbols': [],
                'contributing_strategies': []
            }

        long_positions = [pos for pos in positions if pos.side == 'long']
        short_positions = [pos for pos in positions if pos.side == 'short']

        all_strategies = set()
        for pos in positions:
            all_strategies.update(pos.contributing_strategies)

        return {
            'total_positions': len(positions),
            'total_capital': sum(pos.size_usd for pos in positions),
            'long_positions': len(long_positions),
            'short_positions': len(short_positions),
            'long_capital': sum(pos.size_usd for pos in long_positions),
            'short_capital': sum(pos.size_usd for pos in short_positions),
            'symbols': [pos.symbol for pos in positions],
            'contributing_strategies': list(all_strategies),
            'average_confidence': sum(pos.confidence for pos in positions) / len(positions),
            'position_size_range': {
                'min': min(pos.size_usd for pos in positions),
                'max': max(pos.size_usd for pos in positions),
                'median': sorted([pos.size_usd for pos in positions])[len(positions) // 2]
            }
        }
