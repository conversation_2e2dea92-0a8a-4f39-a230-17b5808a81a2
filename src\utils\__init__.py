"""Utilities module"""

from utils.logging import setup_logging, get_logger
from utils.monitoring import PerformanceMonitor
from utils.helpers import format_currency, format_percentage, calculate_sleep_time, get_next_monitoring_time, get_next_rebalance_time
from utils.shutdown import ShutdownCoordinator, shutdown_coordinator
from utils.exceptions import (
    StatArbError, ConfigurationError, ExchangeConnectionError, DataFetchError,
    OrderExecutionError, PositionManagementError, ValidationError,
    InsufficientDataError, ContractSpecificationError, CredentialError, RiskManagementError,
    APIError, RateLimitError, NetworkError, ExchangeMaintenanceError, CircuitBreakerError,
    InsufficientBalanceError, InvalidOrderError, MarketClosedError
)
from utils.error_handler import (
    ErrorClassifier, ExponentialBackoff, CircuitBreaker, ErrorRecoveryManager, enhanced_retry
)

__all__ = [
    'setup_logging', 'get_logger', 'PerformanceMonitor', 'format_currency',
    'format_percentage', 'calculate_sleep_time', 'get_next_monitoring_time', 'get_next_rebalance_time',
    'ShutdownCoordinator', 'shutdown_coordinator',
    'StatArbError', 'ConfigurationError', 'ExchangeConnectionError', 'DataFetchError',
    'OrderExecutionError', 'PositionManagementError', 'ValidationError',
    'InsufficientDataError', 'ContractSpecificationError', 'CredentialError', 'RiskManagementError',
    'APIError', 'RateLimitError', 'NetworkError', 'ExchangeMaintenanceError', 'CircuitBreakerError',
    'InsufficientBalanceError', 'InvalidOrderError', 'MarketClosedError',
    'ErrorClassifier', 'ExponentialBackoff', 'CircuitBreaker', 'ErrorRecoveryManager', 'enhanced_retry'
]
