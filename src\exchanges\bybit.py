"""
Bybit exchange implementation with comprehensive error handling
"""

import ccxt.async_support as ccxt
import logging
from typing import Dict, List, Optional, Any
from exchanges.base import ExchangeInterface
from utils.error_handler import enhanced_retry

logger = logging.getLogger(__name__)


class BybitExchange(ExchangeInterface):
    """Bybit exchange implementation with comprehensive error handling"""

    def __init__(self):
        self.exchange = None
        self._name = "Bybit"

    @property
    def name(self) -> str:
        return self._name

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize Bybit exchange connection with error handling"""
        try:
            exchange_options = {
                'defaultType': 'swap',
                'recvWindow': 20000,
                'adjustForTimeDifference': True,
            }

            api_key = config.get('api_key', '').strip()
            api_secret = config.get('api_secret', '').strip()

            # Prepare constructor parameters
            constructor_params = {
                'enableRateLimit': True,
                'options': exchange_options,
            }

            # Add API keys if provided
            if api_key and api_secret:
                constructor_params['apiKey'] = api_key
                constructor_params['secret'] = api_secret
                logger.info("🔑 Using API keys for private endpoints")
            else:
                logger.info("🌐 Using public endpoints only (no API keys provided)")

            self.exchange = ccxt.bybit(constructor_params)

            # Handle different trading modes
            if config.get('use_demo', False):
                # Use CCXT's built-in demo trading method if available
                if hasattr(self.exchange, 'enable_demo_trading'):
                    self.exchange.enable_demo_trading(True)
                    logger.info("🎭 Bybit demo trading mode enabled via CCXT")
                else:
                    # Fallback: manually set demo URLs for older CCXT versions
                    logger.info("🔧 Setting demo URLs manually for older CCXT version")
                    self.exchange.urls['api'] = {
                        'spot': 'https://api-demo.bybit.com',
                        'futures': 'https://api-demo.bybit.com',
                        'v2': 'https://api-demo.bybit.com',
                        'public': 'https://api-demo.bybit.com',
                        'private': 'https://api-demo.bybit.com',
                    }
                    logger.info("🎭 Bybit demo trading mode enabled via manual URL override")
            elif config.get('use_testnet', False):
                self.exchange.set_sandbox_mode(True)
                logger.info("🧪 Bybit testnet/sandbox mode enabled")

            # Test public endpoints first
            try:
                markets = await self.fetch_markets()

                # Determine mode for logging
                if config.get('use_demo', False):
                    mode = "demo"
                elif config.get('use_testnet', False):
                    mode = "testnet"
                else:
                    mode = "live"

                logger.info(f"✅ Connected to {self.name} {mode}")
                logger.info(f"✅ Found {len(markets)} markets")

                # Test a public ticker to ensure connectivity
                if markets:
                    test_symbol = list(markets.keys())[0]
                    await self.fetch_ticker(test_symbol)
                    logger.info(f"✅ Public API test successful with {test_symbol}")

            except Exception as public_error:
                logger.error(f"❌ Public API test failed: {public_error}")
                raise

            # Validate API key if provided
            if api_key and api_secret:
                try:
                    balance = await self.fetch_balance()
                    logger.info("✅ API keys are valid and working")
                    logger.info(f"✅ {mode.title()} account balance available: {len(balance)} currencies")
                except Exception as api_error:
                    logger.warning(f"⚠️ API key test failed: {api_error}")
                    logger.warning("⚠️ Will run in public data mode only")
                    # Don't fail initialization, just disable private endpoints
                    self.exchange.apiKey = None
                    self.exchange.secret = None
            else:
                logger.info("ℹ️ No API keys provided - running in public data mode only")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to connect to {self.name}: {e}")
            return False

    @enhanced_retry(max_retries=3, exchange="bybit", operation="fetch_markets")
    async def fetch_markets(self) -> Dict[str, Any]:
        """Fetch markets with enhanced error handling"""
        try:
            logger.debug(f"🔍 Attempting to load markets from {self.name}...")
            markets = await self.exchange.load_markets()
            logger.debug(f"🔍 Successfully loaded {len(markets) if markets else 0} markets")
            return markets
        except Exception as e:
            # Handle demo trading limitations
            if "Demo trading are not supported" in str(e):
                logger.warning(f"⚠️ Demo trading limitation encountered: {e}")
                logger.info("🔄 Attempting to fetch markets without currency info...")
                try:
                    # Try to fetch markets directly without loading currencies
                    markets = await self.exchange.fetch_markets()
                    logger.info(f"✅ Successfully fetched {len(markets)} markets via direct API")
                    return {market['symbol']: market for market in markets}
                except Exception as fallback_error:
                    logger.error(f"❌ Fallback market fetch also failed: {fallback_error}")
                    raise fallback_error
            else:
                logger.error(f"❌ Failed to fetch markets from {self.name}: {e}")
                logger.debug(f"🔍 Exception type: {type(e)}")
                logger.debug(f"🔍 Exception details: {str(e)}")
                import traceback
                logger.debug(f"🔍 Full traceback: {traceback.format_exc()}")
                raise

    @enhanced_retry(max_retries=3, exchange="bybit", operation="fetch_tickers")
    async def fetch_tickers(self) -> Dict[str, Any]:
        """Fetch tickers with enhanced error handling"""
        return await self.exchange.fetch_tickers()

    @enhanced_retry(max_retries=3, exchange="bybit", operation="fetch_ticker")
    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """Fetch ticker for a single symbol with enhanced error handling"""
        return await self.exchange.fetch_ticker(symbol)

    @enhanced_retry(max_retries=3, exchange="bybit", operation="fetch_ohlcv")
    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List[List]:
        """Fetch OHLCV with enhanced error handling"""
        return await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """Fetch funding rate with error handling"""
        try:
            return await self.exchange.fetch_funding_rate(symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate for {symbol} from {self.name}: {e}")
            raise

    async def fetch_funding_rate_history(self, symbol: str, limit: int) -> List[Dict]:
        """Fetch funding rate history with error handling"""
        try:
            return await self.exchange.fetch_funding_rate_history(symbol, limit=limit)
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate history for {symbol} from {self.name}: {e}")
            raise

    async def fetch_balance(self) -> Dict[str, Any]:
        """Fetch balance with error handling"""
        try:
            return await self.exchange.fetch_balance()
        except Exception as e:
            # Handle demo trading limitations
            if "Demo trading are not supported" in str(e):
                logger.warning(f"⚠️ Demo trading balance limitation: {e}")
                logger.info("🔄 Using mock balance for demo trading...")
                # Return a mock balance structure for demo trading
                return {
                    'info': {},
                    'USDT': {'free': 100000.0, 'used': 0.0, 'total': 100000.0},
                    'BTC': {'free': 10.0, 'used': 0.0, 'total': 10.0},
                    'ETH': {'free': 100.0, 'used': 0.0, 'total': 100.0},
                    'free': {'USDT': 100000.0, 'BTC': 10.0, 'ETH': 100.0},
                    'used': {'USDT': 0.0, 'BTC': 0.0, 'ETH': 0.0},
                    'total': {'USDT': 100000.0, 'BTC': 10.0, 'ETH': 100.0}
                }
            else:
                logger.error(f"❌ Failed to fetch balance from {self.name}: {e}")
                raise

    async def fetch_positions(self, symbols: Optional[List[str]] = None) -> List[Dict]:
        """Fetch positions with error handling"""
        try:
            if symbols:
                return await self.exchange.fetch_positions(symbols)
            else:
                return await self.exchange.fetch_positions()
        except Exception as e:
            logger.error(f"❌ Failed to fetch positions from {self.name}: {e}")
            raise

    async def fetch_open_orders(self, symbol: str) -> List[Dict]:
        """Fetch open orders with error handling"""
        try:
            return await self.exchange.fetch_open_orders(symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch open orders for {symbol} from {self.name}: {e}")
            raise

    async def fetch_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Fetch order with error handling"""
        try:
            return await self.exchange.fetch_order(order_id, symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch order {order_id} for {symbol} from {self.name}: {e}")
            raise

    async def fetch_order_book(self, symbol: str) -> Dict[str, Any]:
        """Fetch order book with error handling"""
        try:
            return await self.exchange.fetch_order_book(symbol)
        except Exception as e:
            logger.error(f"❌ Failed to fetch order book for {symbol} from {self.name}: {e}")
            raise

    @enhanced_retry(max_retries=2, exchange="bybit", operation="create_limit_order")
    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict[str, Any]:
        """Create limit order with enhanced error handling"""
        return await self.exchange.create_limit_order(symbol, side, amount, price, params)

    @enhanced_retry(max_retries=2, exchange="bybit", operation="create_market_order")
    async def create_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict[str, Any]:
        """Create market order with enhanced error handling"""
        return await self.exchange.create_market_order(symbol, side, amount, params)

    @enhanced_retry(max_retries=3, exchange="bybit", operation="cancel_order")
    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Cancel order with enhanced error handling"""
        return await self.exchange.cancel_order(order_id, symbol)
