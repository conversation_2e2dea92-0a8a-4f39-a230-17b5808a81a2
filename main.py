#!/usr/bin/env python3

"""
StatArb Carry Trade Strategy - Main Entry Point

A statistical arbitrage carry trade strategy with:
- Multi-exchange support (Bybit, Binance, OKX, Hyperliquid)
- Credential management
- Modular architecture
- Error handling
- Performance optimization
- Contract specification handling
- Data unit validation
"""

import asyncio
import sys
import os
import signal
from pathlib import Path

# Fix Windows console encoding for emojis
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add src to Python path for proper module resolution
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Import modules with proper error handling
try:
    from config import load_config, ConfigValidator
    from storage import SecureStorage, setup_credentials_interactively
    from utils import setup_logging, get_logger, shutdown_coordinator
    from core import StatArbStrategy
except ImportError as e:
    print(f"❌ Import error: {e}")
    print(f"   Src path: {src_path} (exists: {src_path.exists()})")
    raise

logger = get_logger(__name__)


def handle_credentials(config):
    """
    Handle credential loading, detection, and setup.

    Args:
        config: Configuration object

    Returns:
        Updated configuration object with credentials

    Raises:
        SystemExit: If user chooses not to use interactive setup and no credentials found
    """
    logger.info("🔑 Handling credential setup...")

    # Initialize secure storage
    secure_storage = SecureStorage()

    # Check if we have stored credentials
    if not secure_storage.has_credentials():
        # Check if credentials are in config or environment
        exchange_name = config.get('exchange', 'bybit').upper()
        has_credentials = False

        # Check for different credential types based on exchange with standardized env var patterns
        if exchange_name == 'HYPERLIQUID':
            has_credentials = (config.get('wallet_address') and config.get('private_key')) or \
                            (os.getenv('HYPERLIQUID_WALLET_ADDRESS') and os.getenv('HYPERLIQUID_PRIVATE_KEY'))
        elif exchange_name == 'OKX':
            # OKX requires API key, secret, and passphrase
            has_credentials = (config.get('api_key') and config.get('api_secret') and config.get('passphrase')) or \
                            (os.getenv('OKX_API_KEY') and os.getenv('OKX_API_SECRET') and os.getenv('OKX_PASSPHRASE'))
        else:
            # Standard API key/secret for Bybit, Binance - use consistent naming
            env_prefix = exchange_name.upper()
            has_credentials = (config.get('api_key') and config.get('api_secret')) or \
                            (os.getenv(f'{env_prefix}_API_KEY') and os.getenv(f'{env_prefix}_API_SECRET'))

        if not has_credentials:
            logger.warning(f"No credentials found for {exchange_name} in config or environment.")
            logger.info("You can either:")
            logger.info("1. Add credentials to config.yaml")
            logger.info(f"2. Set environment variables:")
            if exchange_name == 'HYPERLIQUID':
                logger.info("   HYPERLIQUID_WALLET_ADDRESS, HYPERLIQUID_PRIVATE_KEY")
            elif exchange_name == 'OKX':
                logger.info("   OKX_API_KEY, OKX_API_SECRET, OKX_PASSPHRASE")
            else:
                env_prefix = exchange_name.upper()
                logger.info(f"   {env_prefix}_API_KEY, {env_prefix}_API_SECRET")
            logger.info("3. Use interactive setup")

            choice = input("\nUse interactive setup? (y/n) [y]: ").strip().lower()
            if choice in ('', 'y', 'yes'):
                credentials = setup_credentials_interactively()
                secure_storage.store_credentials(credentials)

                # Update config with credentials
                for key, value in credentials.items():
                    config.set(key, value)
            else:
                logger.warning("Please add your credentials to config.yaml or set environment variables.")
                raise SystemExit("No credentials provided")
        else:
            logger.info("🔑 Using credentials from config/environment")
    else:
        # Load stored credentials
        stored_credentials = secure_storage.load_credentials()
        logger.info("🔑 Loaded stored credentials")

        # Update config with stored credentials (stored takes precedence)
        for key, value in stored_credentials.items():
            config.set(key, value)

    return config


async def main():
    """Main entry point with graceful shutdown handling"""
    strategy = None

    try:
        # Setup signal handlers for graceful shutdown
        shutdown_coordinator.setup_signal_handlers()

        # Load configuration
        config_file = os.getenv('STATARB_CONFIG', 'config.yaml')
        config = load_config(config_file if os.path.exists(config_file) else None)

        # Setup logging
        setup_logging(
            debug_mode=config.get('debug_mode', False),
            log_file=config.get('log_file', 'statarb_carry_trade.log')
        )

        logger.info("🔧 Starting StatArb Carry Trade Strategy with graceful shutdown support")

        # Handle credentials using dedicated function
        config = handle_credentials(config)

        # Validate configuration
        ConfigValidator.validate(config.to_dict())

        # Display configuration summary
        exchange = config.get('exchange', 'bybit')
        mode = "demo" if config.get('use_demo') else ("testnet" if config.get('use_testnet') else "live")
        capital = config.get('total_capital_usd', 10000)
        peak_funding = config.get('peak_abs_funding_rate', 0.001)
        max_position_pct = config.get('max_position_capital_pct', 25)

        logger.info(f"Configuration: {exchange.title()} {mode.title()}, ${capital:,.0f}, EV-based strategy (peak: {peak_funding*100:.2f}%, max pos: {max_position_pct}%), simulation={config.get('simulation_mode', True)}")

        # Create and initialize strategy
        strategy = StatArbStrategy(config)

        # Initialize exchange connection
        logger.info(f"🔌 Initializing {exchange} exchange connection...")
        await strategy.initialize()

        # Register strategy components with shutdown coordinator
        shutdown_coordinator.register_strategy_components(
            strategy=strategy,
            exchange=strategy.exchange,
            executor=strategy.executor,
            state_manager=strategy.state_manager
        )

        # Create strategy execution task
        strategy_task = asyncio.create_task(strategy.run())
        shutdown_coordinator.register_task(strategy_task)

        # Create shutdown monitoring task
        shutdown_task = asyncio.create_task(shutdown_coordinator.wait_for_shutdown())

        logger.info("🚀 Starting strategy execution with shutdown monitoring...")

        # Wait for either strategy completion or shutdown signal
        _, pending = await asyncio.wait(
            [strategy_task, shutdown_task],
            return_when=asyncio.FIRST_COMPLETED
        )

        # Cancel pending tasks
        for task in pending:
            task.cancel()

        # Check if shutdown was requested
        if shutdown_coordinator.is_shutdown_requested():
            logger.info("🛑 Shutdown signal received - executing graceful shutdown...")

            # Execute graceful shutdown
            shutdown_success = await shutdown_coordinator.execute_graceful_shutdown()

            if shutdown_success:
                logger.info("✅ Graceful shutdown completed successfully")
            else:
                logger.error("❌ Graceful shutdown failed or timed out")
        else:
            # Strategy completed normally
            logger.info("✅ Strategy execution completed normally")

    except KeyboardInterrupt:
        logger.info("🛑 KeyboardInterrupt received - executing graceful shutdown...")
        if strategy:
            shutdown_coordinator.register_strategy_components(
                strategy=strategy,
                exchange=getattr(strategy, 'exchange', None),
                executor=getattr(strategy, 'executor', None),
                state_manager=getattr(strategy, 'state_manager', None)
            )
        await shutdown_coordinator.execute_graceful_shutdown()

    except Exception as e:
        logger.error(f"❌ Strategy failed: {e}")
        import traceback
        logger.debug(f"Full traceback: {traceback.format_exc()}")

        # Attempt graceful shutdown even on error
        if strategy:
            logger.info("🛑 Attempting graceful shutdown after error...")
            shutdown_coordinator.register_strategy_components(
                strategy=strategy,
                exchange=getattr(strategy, 'exchange', None),
                executor=getattr(strategy, 'executor', None),
                state_manager=getattr(strategy, 'state_manager', None)
            )
            await shutdown_coordinator.execute_graceful_shutdown()

        raise


if __name__ == "__main__":
    asyncio.run(main())
