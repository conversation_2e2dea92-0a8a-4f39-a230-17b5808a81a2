"""
Contract specification utilities for handling minimum order sizes and quantity rounding
"""

import logging
import math
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class ContractSpecManager:
    """Manages contract specifications and quantity rounding for exchanges"""
    
    def __init__(self):
        self.market_cache = {}
        self.spec_cache = {}
    
    def cache_market_info(self, markets: Dict[str, Any]):
        """Cache market information for contract specifications"""
        self.market_cache = markets
        logger.info(f"📋 Cached market info for {len(markets)} symbols")
    
    def get_contract_specs(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get contract specifications for a symbol"""
        if symbol in self.spec_cache:
            return self.spec_cache[symbol]
        
        if symbol not in self.market_cache:
            logger.warning(f"⚠️ No market info cached for {symbol}")
            return None
        
        market = self.market_cache[symbol]
        
        # Extract contract specifications with proper null handling
        limits = market.get('limits', {})
        amount_limits = limits.get('amount', {})
        cost_limits = limits.get('cost', {})
        price_limits = limits.get('price', {})
        precision = market.get('precision', {})
        info = market.get('info', {})

        specs = {
            'symbol': symbol,
            'base': market.get('base', ''),
            'quote': market.get('quote', ''),
            'min_amount': amount_limits.get('min') or 0.0,
            'max_amount': amount_limits.get('max') or float('inf'),
            'amount_precision': precision.get('amount') or 8,
            'price_precision': precision.get('price') or 8,
            'min_cost': cost_limits.get('min') or 0.0,
            'max_cost': cost_limits.get('max') or float('inf'),
            'min_price': price_limits.get('min') or 0.0,
            'max_price': price_limits.get('max') or float('inf'),
            'contract_size': market.get('contractSize') or 1.0,
            'lot_size': info.get('lotSizeFilter', {}).get('stepSize') or '0.001',
            'tick_size': info.get('priceFilter', {}).get('tickSize') or '0.01',
        }
        
        # Cache the specifications
        self.spec_cache[symbol] = specs
        
        logger.debug(f"📋 Contract specs for {symbol}: min_amount={specs['min_amount']}, "
                    f"precision={specs['amount_precision']}, lot_size={specs['lot_size']}")
        
        return specs
    
    def round_quantity(self, symbol: str, quantity: float, round_up: bool = True) -> float:
        """Round quantity to meet contract specifications
        
        Args:
            symbol: Trading symbol
            quantity: Raw quantity to round
            round_up: If True, round up to ensure minimum requirements are met
                     If False, round down to avoid exceeding limits
        
        Returns:
            Rounded quantity that meets contract specifications
        """
        specs = self.get_contract_specs(symbol)
        if not specs:
            logger.warning(f"⚠️ No contract specs for {symbol}, returning original quantity")
            return quantity
        
        # Get lot size (step size) for rounding with null safety
        lot_size = float(specs['lot_size']) if specs['lot_size'] else 0.0
        min_amount = specs['min_amount'] or 0.0
        max_amount = specs['max_amount'] or float('inf')
        
        # Round to lot size
        if lot_size > 0:
            if round_up:
                # Round up to next lot size
                rounded_qty = math.ceil(quantity / lot_size) * lot_size
            else:
                # Round down to previous lot size
                rounded_qty = math.floor(quantity / lot_size) * lot_size
        else:
            # Use precision-based rounding if no lot size
            precision = specs['amount_precision']
            if round_up:
                rounded_qty = math.ceil(quantity * (10 ** precision)) / (10 ** precision)
            else:
                rounded_qty = math.floor(quantity * (10 ** precision)) / (10 ** precision)
        
        # Ensure minimum amount is met
        if rounded_qty < min_amount:
            if round_up:
                # Round up to minimum
                rounded_qty = math.ceil(min_amount / lot_size) * lot_size if lot_size > 0 else min_amount
            else:
                # If rounding down and below minimum, return 0 (invalid order)
                logger.warning(f"⚠️ {symbol} quantity {quantity} below minimum {min_amount} after rounding down")
                return 0.0
        
        # Ensure maximum amount is not exceeded
        if rounded_qty > max_amount:
            if round_up:
                # If rounding up exceeds maximum, return 0 (invalid order)
                logger.warning(f"⚠️ {symbol} quantity {quantity} exceeds maximum {max_amount} after rounding up")
                return 0.0
            else:
                # Round down to maximum
                rounded_qty = math.floor(max_amount / lot_size) * lot_size if lot_size > 0 else max_amount
        
        logger.debug(f"📊 {symbol} quantity rounded: {quantity:.8f} → {rounded_qty:.8f} "
                    f"(lot_size={lot_size}, min={min_amount})")
        
        return rounded_qty
    
    def round_price(self, symbol: str, price: float, round_up: bool = True) -> float:
        """Round price to meet contract specifications"""
        specs = self.get_contract_specs(symbol)
        if not specs:
            logger.warning(f"⚠️ No contract specs for {symbol}, returning original price")
            return price
        
        # Get tick size for rounding with null safety
        tick_size = float(specs['tick_size']) if specs['tick_size'] else 0.0
        min_price = specs['min_price'] or 0.0
        max_price = specs['max_price'] or float('inf')
        
        # Round to tick size
        if tick_size > 0:
            if round_up:
                rounded_price = math.ceil(price / tick_size) * tick_size
            else:
                rounded_price = math.floor(price / tick_size) * tick_size
        else:
            # Use precision-based rounding if no tick size
            precision = specs['price_precision']
            if round_up:
                rounded_price = math.ceil(price * (10 ** precision)) / (10 ** precision)
            else:
                rounded_price = math.floor(price * (10 ** precision)) / (10 ** precision)
        
        # Ensure price limits
        rounded_price = max(min_price, min(max_price, rounded_price))
        
        logger.debug(f"💰 {symbol} price rounded: {price:.8f} → {rounded_price:.8f} "
                    f"(tick_size={tick_size})")
        
        return rounded_price
    
    def validate_order_specs(self, symbol: str, amount: float, price: float) -> Tuple[bool, str]:
        """Validate order against contract specifications
        
        Returns:
            (is_valid, error_message)
        """
        specs = self.get_contract_specs(symbol)
        if not specs:
            return False, f"No contract specifications available for {symbol}"
        
        # Check amount limits with null safety
        min_amount = specs['min_amount']
        max_amount = specs['max_amount']

        if min_amount is not None and amount < min_amount:
            return False, f"Amount {amount} below minimum {min_amount}"

        if max_amount is not None and amount > max_amount:
            return False, f"Amount {amount} exceeds maximum {max_amount}"

        # Check price limits with null safety
        min_price = specs['min_price']
        max_price = specs['max_price']

        if min_price is not None and price < min_price:
            return False, f"Price {price} below minimum {min_price}"

        if max_price is not None and price > max_price:
            return False, f"Price {price} exceeds maximum {max_price}"
        
        # Check cost limits (amount * price) with null safety
        cost = amount * price
        min_cost = specs['min_cost']
        max_cost = specs['max_cost']

        if min_cost is not None and cost < min_cost:
            return False, f"Order cost {cost} below minimum {min_cost}"

        if max_cost is not None and cost > max_cost:
            return False, f"Order cost {cost} exceeds maximum {max_cost}"
        
        # Check lot size compliance with null safety and improved precision handling
        lot_size_str = specs['lot_size']
        if lot_size_str:
            lot_size = float(lot_size_str)
            if lot_size > 0:
                # Use more robust floating point comparison
                remainder = amount % lot_size
                tolerance = max(1e-8, lot_size * 1e-6)  # Dynamic tolerance based on lot size
                if remainder > tolerance and (lot_size - remainder) > tolerance:
                    return False, f"Amount {amount} not aligned with lot size {lot_size}"

        # Check tick size compliance with null safety and improved precision handling
        tick_size_str = specs['tick_size']
        if tick_size_str:
            tick_size = float(tick_size_str)
            if tick_size > 0:
                # Use more robust floating point comparison
                remainder = price % tick_size
                tolerance = max(1e-8, tick_size * 1e-6)  # Dynamic tolerance based on tick size
                if remainder > tolerance and (tick_size - remainder) > tolerance:
                    return False, f"Price {price} not aligned with tick size {tick_size}"
        
        return True, "Order specifications valid"
    
    def calculate_position_size_with_rounding(self, symbol: str, target_usd: float,
                                            price: float) -> Tuple[float, float]:
        """Calculate position size with proper rounding to meet contract specs

        Returns:
            (rounded_quantity, actual_usd_value)
        """
        # Round price first to meet contract specifications
        rounded_price = self.round_price(symbol, price, round_up=False)

        # Calculate raw quantity using rounded price
        raw_quantity = target_usd / rounded_price

        # Round quantity to meet contract specifications
        rounded_quantity = self.round_quantity(symbol, raw_quantity, round_up=True)

        # Calculate actual USD value using rounded values
        actual_usd = rounded_quantity * rounded_price

        logger.debug(f"📊 {symbol} position sizing: target=${target_usd:.2f}, "
                    f"price={price:.8f}→{rounded_price:.8f}, "
                    f"raw_qty={raw_quantity:.8f}, rounded_qty={rounded_quantity:.8f}, "
                    f"actual=${actual_usd:.2f}")

        return rounded_quantity, actual_usd
    
    def get_specs_summary(self, symbol: str) -> str:
        """Get human-readable summary of contract specifications"""
        specs = self.get_contract_specs(symbol)
        if not specs:
            return f"No specifications available for {symbol}"
        
        return (f"{symbol} specs: min_amount={specs['min_amount']}, "
                f"lot_size={specs['lot_size']}, tick_size={specs['tick_size']}, "
                f"min_cost={specs['min_cost']}")


# Global instance for easy access
contract_spec_manager = ContractSpecManager()
