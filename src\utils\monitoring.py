"""
Performance monitoring and metrics collection
"""

import time
import logging
from typing import Dict, Any, List
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """Performance monitoring and metrics collection"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'recent_times': deque(maxlen=max_history)
        })
        self.counters = defaultdict(int)
        self.start_times = {}
    
    def start_timer(self, operation: str) -> str:
        """Start timing an operation"""
        timer_id = f"{operation}_{time.time()}"
        self.start_times[timer_id] = time.time()
        return timer_id
    
    def end_timer(self, timer_id: str, operation: str = None):
        """End timing an operation"""
        if timer_id not in self.start_times:
            logger.warning(f"Timer {timer_id} not found")
            return
        
        duration = time.time() - self.start_times.pop(timer_id)
        
        # Extract operation name from timer_id if not provided
        if operation is None:
            operation = timer_id.rsplit('_', 1)[0]
        
        self.record_timing(operation, duration)
    
    def record_timing(self, operation: str, duration: float):
        """Record timing for an operation"""
        metric = self.metrics[operation]
        metric['count'] += 1
        metric['total_time'] += duration
        metric['min_time'] = min(metric['min_time'], duration)
        metric['max_time'] = max(metric['max_time'], duration)
        metric['recent_times'].append(duration)
    
    def increment_counter(self, counter_name: str, amount: int = 1):
        """Increment a counter"""
        self.counters[counter_name] += amount
    
    def get_counter(self, counter_name: str) -> int:
        """Get counter value"""
        return self.counters[counter_name]
    
    def get_timing_stats(self, operation: str) -> Dict[str, Any]:
        """Get timing statistics for an operation"""
        if operation not in self.metrics:
            return {}
        
        metric = self.metrics[operation]
        if metric['count'] == 0:
            return {}
        
        avg_time = metric['total_time'] / metric['count']
        
        # Calculate recent average (last 100 operations)
        recent_times = list(metric['recent_times'])
        recent_avg = sum(recent_times) / len(recent_times) if recent_times else 0
        
        return {
            'count': metric['count'],
            'total_time': metric['total_time'],
            'avg_time': avg_time,
            'min_time': metric['min_time'],
            'max_time': metric['max_time'],
            'recent_avg': recent_avg,
            'recent_count': len(recent_times)
        }
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get all performance statistics"""
        stats = {
            'timings': {},
            'counters': dict(self.counters),
            'summary': {
                'total_operations': sum(m['count'] for m in self.metrics.values()),
                'total_time': sum(m['total_time'] for m in self.metrics.values()),
                'active_timers': len(self.start_times)
            }
        }
        
        for operation in self.metrics:
            stats['timings'][operation] = self.get_timing_stats(operation)
        
        return stats
    
    def log_summary(self, top_n: int = 10):
        """Log performance summary"""
        logger.info("📊 Performance Summary")
        logger.info("=" * 50)
        
        # Log counters
        if self.counters:
            logger.info("📈 Counters:")
            for counter, value in sorted(self.counters.items()):
                logger.info(f"   {counter}: {value:,}")
        
        # Log top operations by count
        operations_by_count = sorted(
            [(op, self.get_timing_stats(op)) for op in self.metrics],
            key=lambda x: x[1].get('count', 0),
            reverse=True
        )[:top_n]
        
        if operations_by_count:
            logger.info("⏱️ Top Operations by Count:")
            for operation, stats in operations_by_count:
                logger.info(f"   {operation}: {stats['count']:,} calls, "
                           f"avg {stats['avg_time']:.3f}s, "
                           f"total {stats['total_time']:.2f}s")
        
        # Log slowest operations
        operations_by_avg_time = sorted(
            [(op, self.get_timing_stats(op)) for op in self.metrics],
            key=lambda x: x[1].get('avg_time', 0),
            reverse=True
        )[:top_n]
        
        if operations_by_avg_time:
            logger.info("🐌 Slowest Operations by Average Time:")
            for operation, stats in operations_by_avg_time:
                logger.info(f"   {operation}: avg {stats['avg_time']:.3f}s, "
                           f"max {stats['max_time']:.3f}s, "
                           f"{stats['count']:,} calls")
    
    def reset(self):
        """Reset all metrics"""
        self.metrics.clear()
        self.counters.clear()
        self.start_times.clear()
        logger.info("🔄 Performance metrics reset")


class TimerContext:
    """Context manager for timing operations"""
    
    def __init__(self, monitor: PerformanceMonitor, operation: str):
        self.monitor = monitor
        self.operation = operation
        self.timer_id = None
    
    def __enter__(self):
        self.timer_id = self.monitor.start_timer(self.operation)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.timer_id:
            self.monitor.end_timer(self.timer_id, self.operation)


# Global performance monitor instance
global_monitor = PerformanceMonitor()


def time_operation(operation: str):
    """Decorator to time function execution"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with TimerContext(global_monitor, operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def get_global_monitor() -> PerformanceMonitor:
    """Get the global performance monitor"""
    return global_monitor
