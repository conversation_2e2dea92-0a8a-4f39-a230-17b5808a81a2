# Multi-Strategy Trading System Configuration
# Main configuration file for shared resources and strategy coordination

# ============================================================================
# STRATEGY CONFIGURATION (modify these for different strategy combinations)
# ============================================================================

# Strategy weights and enablement
strategies:
  stat_arb_carry_trade:
    enabled: true                # Enable/disable this strategy
    weight: 1.0                  # Portfolio weight for this strategy (will be normalized)

# Portfolio combination settings
portfolio_combination:
  enable_position_netting: true  # Net long/short positions by symbol
  min_position_size_usd: 10.0    # Minimum position size after combination
  position_rounding_decimals: 6  # Decimal places for position rounding

# Performance tracking settings
performance_tracking:
  max_performance_history_days: 90        # Days of performance history to keep
  enable_detailed_performance_tracking: true  # Track detailed position-level data
  performance_tracking_file: "performance_history.json"  # Performance data file

# ============================================================================
# FREQUENTLY CHANGED PARAMETERS (modify these for different trading scenarios)
# ============================================================================

# Control Flags (most commonly toggled)
simulation_mode: true             # Enable simulation mode (no real trades)
immediate_start: true             # Start immediately without waiting
debug_mode: false                 # Enable debug logging

# Exchange Configuration
exchange: bybit                   # Supported: bybit, binance, okx, hyperliquid
use_testnet: false               # Use testnet for testing
use_demo: true                   # Use demo trading (if supported)

# Shared Strategy Parameters (used by all strategies unless overridden)
total_capital_usd: 10000         # Total capital to deploy across all strategies

# Shared risk management settings
buffer_zone_tolerance_percentage: 5.0  # Buffer zone around target position (% of individual coin's USD position)

# Shared beta projection settings (used by strategies that enable it)
enable_beta_projection: true     # Enable beta-neutral portfolio construction
market_index_symbol: "BTCUSDT"   # Market index symbol for beta calculation (BTC as crypto market proxy)
beta_neutrality_tolerance: 0.05  # Maximum allowed portfolio beta deviation from zero (±5%)
beta_optimization_max_weight_change: 0.20  # Maximum allowed weight change during beta optimization (20%)

# ============================================================================
# STRATEGY MANAGEMENT PARAMETERS (adjust occasionally)
# ============================================================================

# Multi-strategy execution settings
max_concurrent_strategies: 4     # Maximum strategies to run in parallel
strategy_timeout_seconds: 300    # Timeout for individual strategy execution (5 minutes)

# ============================================================================
# MODERATELY CHANGED PARAMETERS (adjust occasionally)
# ============================================================================

# API Credentials - Add your keys here (or use environment variables)
api_key: ""                      # Your API key (or set BYBIT_API_KEY env var)
api_secret: ""                   # Your API secret (or set BYBIT_API_SECRET env var)

# Additional credentials for other exchanges:
# For OKX (uncomment and fill when using OKX):
# passphrase: "your_okx_passphrase"                  # OKX requires passphrase

# For Hyperliquid (uncomment and fill when using Hyperliquid):
# wallet_address: "******************************************"  # Your wallet address
# private_key: "your_private_key_here"               # Your private key (keep secure!)

# Shared trading cost adjustments per exchange (used by all strategies)
trading_cost_adjustment:
  bybit: 0.0001                  # 0.01% for Bybit
  binance: 0.0001                # 0.01% for Binance
  okx: 0.0001                    # 0.01% for OKX
  hyperliquid: 0.0000125         # 0.00125% for Hyperliquid (lower fees)

# Shared filtering parameters (used by all strategies unless overridden)
exclude_new_listings_days: 60    # Exclude coins listed within this many days
min_historical_data_days: 60     # Minimum days of historical data required

# Shared execution settings (used by all strategies)
min_orderbook_depth: 3           # Minimum orderbook depth required
max_spread_threshold: 0.05       # Maximum spread threshold (5%)
min_close_threshold_usd: 10      # Minimum USD value to trigger position closure
monitor_position_alignment: false # Check position alignment during monitoring (optional)
min_leverage: 0.05               # Minimum position leverage
max_leverage: 5.0                # Maximum position leverage

# Randomized Batch Execution Settings (occasionally tuned)
min_batch_size: 3                # Minimum number of batches per coin
max_batch_size: 5                # Maximum number of batches per coin
min_delta_percentage: 15         # Minimum percentage of delta per batch (%)
max_delta_percentage: 35         # Maximum percentage of delta per batch (%)
min_batch_interval_seconds: 60   # Minimum delay between batches
max_batch_interval_seconds: 300  # Maximum delay between batches
min_orderbook_levels: 3          # Minimum orderbook levels to split orders across
max_orderbook_levels: 7          # Maximum orderbook levels to split orders across
max_execution_iterations: 10     # Maximum iterations before execution fails (prevents infinite loops)
order_settle_time_seconds: 300   # Time to wait for orders to settle before reconciliation (5 minutes)
aggressive_execution_mode: false # Use market orders as fallback if post-only orders fail repeatedly

# Delta Reconciliation Configuration
delta_tolerance_percentage: 0.5  # Position tolerance for delta reconciliation (% of capital USD value)
# Note: Delta reconciliation continues until all position deltas are within this USD tolerance

# Post-Only Order Optimization Settings
post_only_max_retries: 3         # Maximum retries for post-only order rejections
post_only_retry_delay_ms: 150    # Delay between post-only retries (milliseconds)
order_price_offset_pct: 0.0      # Price offset from best bid/ask (0% = exact bid/ask, 0.001 = 0.1% offset)

# ============================================================================
# RARELY CHANGED PARAMETERS (advanced settings, usually keep defaults)
# ============================================================================

# Timing Configuration
# Note: Rebalancing occurs at 23:00 UTC daily (1 hour before funding)
# Monitoring occurs at 23:00, 07:00, 15:00 UTC (1 hour before each funding time)
max_sleep_seconds: 3600          # Maximum sleep duration between checks (1 hour)
default_sleep_seconds: 3600      # Default sleep when no events scheduled (1 hour)
monitoring_window_seconds: 300   # Window around monitoring time to trigger check (5 minutes)

# Cache Configuration
cache_default_ttl: 300           # Default cache TTL in seconds (5 minutes)
cache_max_size: 1000             # Maximum cache entries
cache_gc_interval: 300           # Garbage collection interval in seconds (5 minutes)

# Data Analysis
default_volatility: 0.2          # Default volatility fallback (20%)
default_beta: 1.0                # Default beta fallback (1.0 = market beta)

# Beta Calculation Settings (advanced)
beta_calculation_days: 60        # Number of days for rolling beta calculation
beta_cache_ttl: 3600            # Beta cache TTL in seconds (1 hour)

# Performance Optimization Flags
async_batch_execution: true      # Enable asynchronous batch execution (recommended)

# Logging Configuration
log_file: "statarb_carry_trade.log"  # Log file name (will be placed in logs/ directory)

# Performance and Caching (advanced settings)
max_concurrent_api_calls: 4      # Further reduced for demo rate limits
rate_limit_delay_ms: 200         # Increased delay for demo
cache_ttl_seconds: 300           # Cache time-to-live in seconds
symbol_processing_batch_size: 50 # Further reduced for demo
cache_cleanup_interval: 600      # Cache cleanup interval in seconds
order_history_retention_days: 7  # Order history retention in days

# ============================================================================
# DEFAULT PARAMETERS (system defaults - rarely need modification)
# ============================================================================

# Default values for all system parameters
defaults:
  # Core strategy defaults
  default_volatility: 0.20            # Default volatility fallback (20%)
  default_beta: 1.0                    # Default beta fallback (1.0 = market beta)

  # Position sizing defaults (use values from main config to avoid duplicates)
  # min_leverage and max_leverage are defined above in main config

  # Order execution defaults (use values from main config to avoid duplicates)
  # min_orderbook_depth, max_spread_threshold, min_close_threshold_usd defined above

  # Data validation defaults
  max_price_deviation: 0.10            # Maximum price deviation for validation (10%)
  min_volume_threshold: 1000           # Minimum volume threshold for validation

  # Cache defaults
  cache_default_ttl: 300               # Default cache TTL in seconds (5 minutes)
  cache_max_size: 1000                 # Maximum cache entries
  cache_gc_interval: 300               # Garbage collection interval in seconds (5 minutes)

  # Memory management defaults
  max_memory_usage_mb: 512             # Maximum memory usage in MB
  memory_check_interval: 300           # Memory check interval in seconds
  memory_cleanup_threshold: 0.8        # Memory cleanup threshold (80%)

  # Performance optimization defaults
  concurrent_calculation_batch_size: 10 # Batch size for parallel calculations
  cleanup_interval_seconds: 3600       # Cleanup interval for long-running processes (1 hour)

# ============================================================================
# TECHNICAL/ADVANCED PARAMETERS (rarely modified, for experts only)
# ============================================================================

# Execution Rate Limiting (advanced)
execution_rate_limiting:
  # Async batch execution settings
  async_batches_per_coin: true   # Enable asynchronous batch execution per coin
  max_concurrent_coins: 4        # Maximum coins executing simultaneously (reduced for demo)
  max_concurrent_batches: 12     # Global limit on concurrent batches across all coins (reduced)
  batch_semaphore_size: 4        # Maximum concurrent batches per coin (reduced)

  # Staggered execution to prevent rate limits
  coin_start_delay_ms: 500       # Delay between starting each coin's execution (increased)
  batch_start_delay_ms: 100      # Delay between starting each batch (increased)
  order_placement_delay_ms: 100  # Delay between individual order placements (increased)

  # Execution phases to spread load
  execution_phases:
    enabled: true                # Enable phased execution
    phase_size: 20               # Number of coins per phase (reduced)
    phase_delay_seconds: 30      # Delay between phases (increased)

  # Emergency throttling
  emergency_throttling:
    enabled: true                # Enable emergency throttling
    rate_limit_threshold: 3      # Rate limit errors before throttling (reduced)
    throttle_delay_seconds: 60   # Throttling delay (increased)
    max_throttle_time: 300       # Maximum throttling time (increased)

# API Rate Limiting Configuration (advanced)
api_rate_limiting:
  # Global rate limiting settings
  max_requests_per_second: 5     # Maximum API requests per second (reduced for demo)
  max_concurrent_requests: 4     # Maximum concurrent API requests (reduced)
  burst_limit: 10                # Burst limit for short periods (reduced)

  # Order placement rate limiting
  order_placement:
    max_orders_per_second: 2     # Maximum orders per second (reduced for demo)
    max_orders_per_minute: 60    # Maximum orders per minute (reduced)
    batch_delay_ms: 300          # Delay between order batches (increased)
    retry_delay_ms: 1500         # Delay before retrying failed orders (increased)
    max_retries: 3               # Maximum retry attempts per order

  # Exchange-specific rate limits (requests per second)
  exchange_limits:
    bybit: 5                     # Bybit: 5 req/sec (reduced for demo)
    binance: 20                  # Binance: 20 req/sec
    okx: 20                      # OKX: 20 req/sec
    hyperliquid: 50              # Hyperliquid: 50 req/sec

  # Adaptive rate limiting
  adaptive_limiting:
    enabled: true                # Enable adaptive rate limiting
    backoff_multiplier: 2.0      # Backoff multiplier on rate limit hit
    recovery_time_seconds: 30    # Time to recover from rate limiting
    max_backoff_seconds: 300     # Maximum backoff time

  # Enhanced error handling and recovery
  error_handling:
    # Retry configuration
    max_retries: 3               # Default max retries for most operations
    rate_limit_max_retries: 5    # Max retries for rate limit errors
    network_max_retries: 3       # Max retries for network errors

    # Exponential backoff configuration
    retry_base_delay: 1.0        # Base delay in seconds
    retry_max_delay: 300.0       # Maximum delay in seconds (5 minutes)
    retry_multiplier: 2.0        # Backoff multiplier
    retry_jitter: true           # Add random jitter to delays

    # Network timeout configuration
    connect_timeout: 30          # Connection timeout in seconds
    read_timeout: 60             # Read timeout in seconds
    total_timeout: 120           # Total request timeout in seconds

    # Connection pool configuration
    max_connections: 100         # Maximum connections in pool
    max_keepalive_connections: 20 # Maximum keep-alive connections
    keepalive_expiry: 5          # Keep-alive expiry in seconds

  # Circuit breaker for API failures (enhanced)
  circuit_breaker:
    failure_threshold: 5         # Failures before circuit opens
    timeout_seconds: 60          # Circuit breaker timeout
    half_open_max_calls: 3       # Max calls in half-open state
