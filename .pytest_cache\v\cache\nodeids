["tests/test_buffer_zones.py::TestBufferZones::test_buffer_zone_closure_threshold", "tests/test_buffer_zones.py::TestBufferZones::test_buffer_zone_edge_cases", "tests/test_buffer_zones.py::TestBufferZones::test_buffer_zone_multiple_positions", "tests/test_buffer_zones.py::TestBufferZones::test_buffer_zone_no_trade", "tests/test_buffer_zones.py::TestBufferZones::test_buffer_zone_side_mismatch", "tests/test_buffer_zones.py::TestBufferZones::test_buffer_zone_trade_down", "tests/test_buffer_zones.py::TestBufferZones::test_buffer_zone_trade_up", "tests/test_critical_fixes.py::TestBufferZoneLogic::test_buffer_zone_calculation_logic", "tests/test_critical_fixes.py::TestDataValidationEnhancements::test_funding_rate_format_detection", "tests/test_critical_fixes.py::TestDataValidationEnhancements::test_funding_rate_validation_limits", "tests/test_critical_fixes.py::TestDataValidationEnhancements::test_volume_validation", "tests/test_critical_fixes.py::TestSigmoidFunctionRobustness::test_sigmoid_logic", "tests/test_critical_fixes.py::TestVolumeUnitConsistency::test_volume_conversion_logic", "tests/test_ev_config_validation.py::TestEVConfigValidation::test_leverage_bounds_validation", "tests/test_ev_config_validation.py::TestEVConfigValidation::test_valid_ev_config", "tests/test_ev_position_selection.py::TestEVPositionSelection::test_ev_weight_calculation", "tests/test_ev_position_selection.py::TestEVPositionSelection::test_raw_funding_secondary_ranking", "tests/test_ev_position_selection.py::TestEVPositionSelection::test_three_tier_ranking_system", "tests/test_ev_volatility_calculations.py::TestVolatilityCalculations::test_volatility_fallback_handling"]