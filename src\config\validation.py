"""
Configuration validation module
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class ConfigValidator:
    """Validates configuration parameters"""
    
    @staticmethod
    def validate(config: Dict[str, Any]) -> bool:
        """Validate configuration parameters"""
        try:
            logger.info("🔧 Validating configuration parameters...")
            
            # Validate required fields
            required_fields = ['exchange', 'total_capital_usd']
            for field in required_fields:
                if field not in config or config[field] is None:
                    raise ValueError(f"Required config field missing: {field}")

            # Check for deprecated fields and migrate/warn
            deprecated_fields = {
                'position_percentile': {
                    'message': "⚠️ 'position_percentile' is deprecated in EV-based strategy. Use 'peak_abs_funding_rate' and 'min_positions_per_leg' instead.",
                    'migration': None  # No automatic migration possible
                },
                'position_selection_buffer': {
                    'message': "⚠️ 'position_selection_buffer' is deprecated in EV-based strategy.",
                    'migration': None  # No automatic migration possible
                },
                'circuit_breaker_threshold': {
                    'message': "⚠️ 'circuit_breaker_threshold' is deprecated. Error handling is now managed by ErrorRecoveryManager.",
                    'migration': None
                },
                'circuit_breaker_timeout': {
                    'message': "⚠️ 'circuit_breaker_timeout' is deprecated. Error handling is now managed by ErrorRecoveryManager.",
                    'migration': None
                },
                'position_buffer_tolerance': {
                    'message': "⚠️ 'position_buffer_tolerance' is deprecated. Use 'buffer_zone_tolerance_percentage' instead.",
                    'migration': 'buffer_zone_tolerance_percentage'
                },
                'min_position_change': {
                    'message': "⚠️ 'min_position_change' is deprecated. Buffer zone logic now handles minimum position changes via 'buffer_zone_tolerance_percentage'.",
                    'migration': None
                }
            }

            # Process deprecated fields
            for field, info in deprecated_fields.items():
                if field in config:
                    logger.warning(info['message'])

                    # Attempt automatic migration if possible
                    if info['migration'] and info['migration'] not in config:
                        if field == 'position_buffer_tolerance':
                            # Convert from decimal to percentage
                            old_value = config[field]
                            new_value = old_value * 100  # Convert 0.05 to 5.0
                            config[info['migration']] = new_value
                            logger.info(f"🔄 Migrated {field}={old_value} to {info['migration']}={new_value}")

                    # Remove deprecated field
                    del config[field]
                    logger.info(f"🧹 Removed deprecated field: {field}")

            # Set default values for optional EV parameters if not provided
            config.setdefault('peak_abs_funding_rate', 0.001)
            config.setdefault('max_position_capital_pct', 25)
            config.setdefault('min_positions_per_leg', 2)
            
            # Validate numeric ranges
            validations = [
                ('total_capital_usd', 100, 10000000, "Total capital must be between $100 and $10M"),
                ('min_daily_volume_usd', 1000, 1000000000, "Min daily volume must be between $1K and $1B"),
                ('max_abs_funding_rate', 0.0001, 0.1, "Max funding rate must be between 0.01% and 10%"),
                # EV-based strategy parameters
                ('peak_abs_funding_rate', 0.00001, 0.01, "Peak funding rate must be between 0.001% and 1%"),
                ('max_position_capital_pct', 1, 50, "Max position capital percentage must be between 1% and 50%"),
                ('min_positions_per_leg', 1, 20, "Min positions per leg must be between 1 and 20"),
                # trading_cost_adjustment is now handled separately as it can be dict or float
                ('target_volatility', 0.01, 2.0, "Target volatility must be between 1% and 200%"),
                ('min_leverage', 0.01, 1.0, "Min leverage must be between 0.01x and 1.0x"),
                ('max_leverage', 1.0, 10.0, "Max leverage must be between 1.0x and 10.0x"),
                ('min_batch_size', 1, 20, "Min batch size must be between 1 and 20"),
                ('max_batch_size', 1, 50, "Max batch size must be between 1 and 50"),
                ('min_batch_interval_seconds', 1, 3600, "Min batch interval must be between 1s and 1h"),
                ('max_batch_interval_seconds', 1, 7200, "Max batch interval must be between 1s and 2h"),
                ('min_orderbook_levels', 1, 20, "Min orderbook levels must be between 1 and 20"),
                ('max_orderbook_levels', 1, 50, "Max orderbook levels must be between 1 and 50"),
                ('max_order_monitoring_seconds', 60, 7200, "Max monitoring time must be between 1min and 2h"),
                ('max_fetch_retries_per_order', 1, 20, "Max fetch retries must be between 1 and 20"),
            ]
            
            for field, min_val, max_val, message in validations:
                if field in config:
                    value = config[field]
                    if not isinstance(value, (int, float)) or value < min_val or value > max_val:
                        raise ValueError(f"{message}. Got: {value}")
            
            # Validate logical relationships
            if config.get('min_batch_size', 1) > config.get('max_batch_size', 50):
                raise ValueError("Min batch size cannot be greater than max batch size")
            
            if config.get('min_batch_interval_seconds', 30) > config.get('max_batch_interval_seconds', 300):
                raise ValueError("Min batch interval cannot be greater than max batch interval")
            
            if config.get('min_orderbook_levels', 5) > config.get('max_orderbook_levels', 10):
                raise ValueError("Min orderbook levels cannot be greater than max orderbook levels")
            
            if config.get('min_leverage', 0.1) > config.get('max_leverage', 5.0):
                raise ValueError("Min leverage cannot be greater than max leverage")

            # Validate EV-based strategy parameters
            peak_funding = config.get('peak_abs_funding_rate', 0.001)
            max_funding = config.get('max_abs_funding_rate', 0.003)
            if peak_funding > max_funding:
                raise ValueError(f"Peak funding rate ({peak_funding}) cannot be greater than max funding rate ({max_funding})")

            # Validate that max position capital percentage makes sense
            max_pos_pct = config.get('max_position_capital_pct', 25)
            min_positions = config.get('min_positions_per_leg', 2)
            if max_pos_pct * min_positions * 2 > 100:  # 2 legs (long + short)
                logger.warning(f"⚠️ Max position capital ({max_pos_pct}%) × min positions per leg ({min_positions}) × 2 legs "
                             f"= {max_pos_pct * min_positions * 2}% > 100%. This may prevent full capital deployment.")

            # Validate trading cost adjustment (can be dict or float)
            trading_cost = config.get('trading_cost_adjustment')
            if trading_cost is not None:
                if isinstance(trading_cost, dict):
                    # Validate each exchange's trading cost
                    for exchange, cost in trading_cost.items():
                        if not isinstance(cost, (int, float)) or cost < 0 or cost > 0.01:
                            raise ValueError(f"Trading cost for {exchange} must be between 0% and 1%. Got: {cost}")
                elif isinstance(trading_cost, (int, float)):
                    # Validate single trading cost value
                    if trading_cost < 0 or trading_cost > 0.01:
                        raise ValueError(f"Trading cost adjustment must be between 0% and 1%. Got: {trading_cost}")
                else:
                    raise ValueError("Trading cost adjustment must be a number or dictionary of exchange-specific values")

            # Validate buffer zone parameters
            buffer_tolerance = config.get('buffer_zone_tolerance_percentage', 5.0)
            if not isinstance(buffer_tolerance, (int, float)) or buffer_tolerance < 0 or buffer_tolerance > 50:
                raise ValueError(f"Buffer zone tolerance percentage must be between 0% and 50%. Got: {buffer_tolerance}")

            delta_tolerance = config.get('delta_tolerance_percentage', 0.5)
            if not isinstance(delta_tolerance, (int, float)) or delta_tolerance < 0 or delta_tolerance > 10:
                raise ValueError(f"Delta tolerance percentage must be between 0% and 10%. Got: {delta_tolerance}")

            min_close_threshold = config.get('min_close_threshold_usd', 10)
            if not isinstance(min_close_threshold, (int, float)) or min_close_threshold < 0:
                raise ValueError(f"Minimum close threshold must be non-negative USD amount. Got: {min_close_threshold}")

            # Validate exchange
            supported_exchanges = ['bybit', 'binance', 'okx', 'hyperliquid']
            exchange = config.get('exchange', '').lower()
            if exchange not in supported_exchanges:
                raise ValueError(f"Unsupported exchange: {exchange}. Supported: {supported_exchanges}")

            logger.info("✅ Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            raise
