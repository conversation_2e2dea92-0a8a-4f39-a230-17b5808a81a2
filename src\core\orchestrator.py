"""
Multi-strategy orchestrator for coordinating data fetching, strategy execution, and portfolio combination

This module replaces the single-strategy approach with a multi-strategy framework that:
1. Fetches and caches data once for all strategies
2. Executes strategies in parallel
3. Combines strategy portfolios with proper netting
4. Executes the final combined portfolio
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from config import Config
from exchanges import ExchangeFactory
from storage import StateManager
from utils import PerformanceMonitor, get_next_monitoring_time, get_next_rebalance_time, calculate_sleep_time, shutdown_coordinator
from utils.error_handler import ErrorRecoveryManager, ErrorClassifier
from data import DataFetcher, DataAnalyzer, DataCache
from execution.randomized_executor import RandomizedExecutor

# Import strategy framework
from strategies import StrategyManager
from strategies.stat_arb_carry_trade import StatArbCarryTradeStrategy
from portfolio import PortfolioCombiner, PerformanceTracker

logger = logging.getLogger(__name__)


class MultiStrategyOrchestrator:
    """
    Main orchestrator for multi-strategy trading system
    
    Coordinates data fetching, strategy execution, portfolio combination,
    and trade execution across multiple strategies.
    """
    
    def __init__(self, config: Config):
        """
        Initialize the multi-strategy orchestrator
        
        Args:
            config: Main configuration object
        """
        self.config = config
        self.exchange = None
        self.state_manager = StateManager()
        self.performance_monitor = PerformanceMonitor()
        self.error_manager = ErrorRecoveryManager(config)
        self.running = False

        # Initialize shared data and execution modules
        self.data_cache = DataCache()
        self.data_fetcher = None
        self.data_analyzer = None
        self.executor = None

        # Initialize multi-strategy components
        self.strategy_manager = StrategyManager(config.to_dict())
        self.portfolio_combiner = PortfolioCombiner(config.to_dict())
        self.performance_tracker = PerformanceTracker(config.to_dict())

        logger.info("🏗️ Multi-strategy orchestrator initialized")
    
    async def initialize(self):
        """Initialize exchange connection and all strategy components"""
        try:
            # Create exchange instance
            exchange_name = self.config.get('exchange', 'bybit')
            self.exchange = ExchangeFactory.create_exchange(exchange_name)
            
            # Get exchange-specific configuration
            exchange_config = self.config.get_exchange_config(exchange_name)
            
            # Initialize the exchange
            success = await self.exchange.initialize(exchange_config)
            if not success:
                raise Exception(f"Failed to initialize {exchange_name} exchange")
            
            logger.info(f"✅ Successfully initialized {exchange_name} exchange")

            # Initialize shared data and execution modules
            self.data_fetcher = DataFetcher(self.exchange, self.config.to_dict(), self.data_cache, self.performance_monitor)
            self.data_analyzer = DataAnalyzer(self.data_fetcher, self.config.to_dict())
            self.executor = RandomizedExecutor(self.exchange, self.config.to_dict())

            logger.info("✅ Initialized shared data fetcher, analyzer, and execution modules")

            # Initialize and register strategies
            await self._initialize_strategies()

            # Load existing state
            state_summary = self.state_manager.get_summary()
            logger.info(f"📁 Loaded state: {state_summary}")

            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize orchestrator: {e}")
            raise
    
    async def _initialize_strategies(self):
        """Initialize and register all configured strategies"""
        try:
            # Get strategy configuration from main config
            strategies_config = self.config.get('strategies', {})
            
            if not strategies_config:
                logger.warning("⚠️ No strategies configured, adding default StatArb Carry Trade")
                # Add default strategy if none configured
                strategies_config = {
                    'stat_arb_carry_trade': {
                        'enabled': True,
                        'weight': 1.0
                    }
                }
            
            logger.info(f"🔧 Initializing {len(strategies_config)} configured strategies...")
            
            # Initialize each configured strategy
            for strategy_name, strategy_config in strategies_config.items():
                if not strategy_config.get('enabled', True):
                    logger.info(f"⏭️ Skipping disabled strategy: {strategy_name}")
                    continue
                
                weight = strategy_config.get('weight', 1.0)
                
                try:
                    if strategy_name == 'stat_arb_carry_trade':
                        # Initialize StatArb Carry Trade strategy
                        strategy = StatArbCarryTradeStrategy(
                            data_fetcher=self.data_fetcher,
                            data_analyzer=self.data_analyzer,
                            exchange=self.exchange,
                            main_config=self.config.to_dict()
                        )
                        
                        # Register with strategy manager
                        self.strategy_manager.register_strategy(strategy, weight)
                        
                        logger.info(f"✅ Initialized and registered strategy: {strategy_name} (weight: {weight})")
                    
                    else:
                        logger.warning(f"⚠️ Unknown strategy type: {strategy_name}")
                        continue
                
                except Exception as e:
                    logger.error(f"❌ Failed to initialize strategy {strategy_name}: {e}")
                    continue
            
            # Validate all registered strategies
            if not self.strategy_manager.validate_strategies():
                raise Exception("Strategy validation failed")
            
            registered_strategies = self.strategy_manager.get_registered_strategies()
            logger.info(f"✅ Successfully initialized {len(registered_strategies)} strategies: {registered_strategies}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize strategies: {e}")
            raise
    
    async def run(self):
        """Main orchestrator execution loop"""
        self.running = True
        logger.info("🚀 Starting multi-strategy orchestrator execution loop")

        # Check immediate_start setting
        immediate_start = self.config.get('immediate_start', True)

        if not immediate_start:
            # Wait until next 23:00 UTC before starting
            next_start_time = get_next_rebalance_time()
            now = datetime.now(timezone.utc)
            wait_seconds = (next_start_time - now).total_seconds()

            if wait_seconds > 0:
                logger.info(f"⏰ immediate_start=False: Waiting {wait_seconds:.0f} seconds until {next_start_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")
                await asyncio.sleep(wait_seconds)
                logger.info("⏰ Start time reached - beginning orchestrator execution")

        try:
            while self.running and not shutdown_coordinator.is_shutdown_requested():
                # Check if it's time for rebalancing
                if await self._should_rebalance():
                    logger.info("⚖️ Starting multi-strategy rebalancing process...")
                    await self._rebalance_portfolio()

                # Check if it's time for monitoring
                if await self._should_monitor():
                    logger.info("👁️ Starting position monitoring...")
                    await self._monitor_positions()

                # Calculate sleep time until next event
                sleep_time = self._calculate_next_sleep()
                max_sleep = self.config.get('max_sleep_seconds', 3600)
                default_sleep = self.config.get('default_sleep_seconds', 3600)

                if sleep_time > 0:
                    actual_sleep = min(sleep_time, max_sleep)
                    logger.info(f"😴 Sleeping for {actual_sleep:.1f} seconds until next event")

                    # Sleep in smaller chunks to check for shutdown signals
                    sleep_chunk = 10.0  # Check every 10 seconds
                    remaining_sleep = actual_sleep

                    while remaining_sleep > 0 and not shutdown_coordinator.is_shutdown_requested():
                        chunk_sleep = min(sleep_chunk, remaining_sleep)
                        await asyncio.sleep(chunk_sleep)
                        remaining_sleep -= chunk_sleep
                else:
                    logger.info(f"😴 No events scheduled - sleeping for {default_sleep:.1f} seconds")

                    # Sleep in smaller chunks to check for shutdown signals
                    sleep_chunk = 10.0  # Check every 10 seconds
                    remaining_sleep = default_sleep

                    while remaining_sleep > 0 and not shutdown_coordinator.is_shutdown_requested():
                        chunk_sleep = min(sleep_chunk, remaining_sleep)
                        await asyncio.sleep(chunk_sleep)
                        remaining_sleep -= chunk_sleep
                
        except KeyboardInterrupt:
            logger.info("⏹️ Orchestrator stopped by user")
        except Exception as e:
            logger.error(f"❌ Orchestrator execution failed: {e}")
            raise
        finally:
            self.running = False
            if shutdown_coordinator.is_shutdown_requested():
                logger.info("🛑 Shutdown requested - skipping orchestrator cleanup (handled by shutdown coordinator)")
            else:
                await self._cleanup()
    
    async def _should_rebalance(self) -> bool:
        """Check if it's time to rebalance (at 23:00 UTC daily)"""
        now = datetime.now(timezone.utc)
        last_rebalance = self.state_manager.get_last_rebalance()

        # If immediate_start is enabled and no rebalance today, rebalance now
        immediate_start = self.config.get('immediate_start', False)
        if immediate_start:
            if not last_rebalance:
                logger.info("🚀 immediate_start=True: No previous rebalance - starting now")
                return True

            # Check if we haven't rebalanced today
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            if last_rebalance < today_start:
                logger.info("🚀 immediate_start=True: No rebalance today - starting now")
                return True

        # If no previous rebalance, rebalance now
        if not last_rebalance:
            return True

        # Check if we're at or past 23:00 and haven't rebalanced today
        if now.hour >= 23:
            # Check if last rebalance was before today's 23:00
            today_rebalance_time = now.replace(hour=23, minute=0, second=0, microsecond=0)
            return last_rebalance < today_rebalance_time

        return False
    
    async def _should_monitor(self) -> bool:
        """Check if it's time for monitoring"""
        next_monitoring = get_next_monitoring_time()
        now = datetime.now(timezone.utc)

        # Monitor if we're within the configured window of monitoring time
        monitoring_window = self.config.get('monitoring_window_seconds', 300)
        time_diff = abs((next_monitoring - now).total_seconds())
        return time_diff <= monitoring_window
    
    def _calculate_next_sleep(self) -> float:
        """Calculate sleep time until next event"""
        now = datetime.now(timezone.utc)

        # Calculate time until next rebalance (next 23:00)
        next_rebalance = get_next_rebalance_time()
        time_to_rebalance = (next_rebalance - now).total_seconds()

        # Calculate time until next monitoring
        next_monitoring = get_next_monitoring_time()
        time_to_monitoring = calculate_sleep_time(next_monitoring)

        # Return minimum time (next event)
        return max(0, min(time_to_rebalance, time_to_monitoring))

    async def _rebalance_portfolio(self):
        """Execute multi-strategy rebalancing process"""
        try:
            timer_id = self.performance_monitor.start_timer("multi_strategy_rebalance")

            logger.info("🔄 Starting multi-strategy portfolio rebalancing...")

            # Step 1: Pre-fetch and cache all market data
            logger.info("📡 Pre-fetching market data for all strategies...")
            await self._prefetch_market_data()

            # Step 2: Execute all strategies in parallel
            logger.info("🚀 Executing all strategies in parallel...")
            strategy_results = await self.strategy_manager.execute_all_strategies()

            if not strategy_results:
                logger.warning("⚠️ No strategy results - skipping rebalancing")
                return

            # Step 3: Get strategy weights and combine portfolios
            logger.info("🔄 Combining strategy portfolios...")
            strategy_weights = self.strategy_manager.get_strategy_weights()
            combined_positions = self.portfolio_combiner.combine_portfolios(strategy_results, strategy_weights)

            if not combined_positions:
                logger.warning("⚠️ No combined positions - skipping execution")
                # Still update rebalance time to avoid infinite retries
                self.state_manager.set_last_rebalance(datetime.now(timezone.utc))
                self.state_manager.save_state()
                return

            # Step 4: Convert combined positions to execution format
            logger.info("📋 Converting combined positions for execution...")
            execution_positions = self._convert_to_execution_format(combined_positions)

            # Step 5: Execute the combined portfolio
            if execution_positions:
                if not self.config.get('simulation_mode', True):
                    logger.info("📋 Executing combined portfolio with randomized batching...")
                    success = await self.executor.execute_to_target_positions(execution_positions)

                    if success:
                        logger.info("✅ Combined portfolio executed successfully")
                        # Update state with combined positions
                        self._update_state_with_combined_positions(combined_positions)
                    else:
                        logger.error("❌ Portfolio execution failed - NOT updating rebalance timestamp")
                        return
                else:
                    logger.info("🎭 Simulation mode - portfolio execution simulated")
                    # In simulation mode, update state with combined positions
                    self._update_state_with_combined_positions(combined_positions)
            else:
                logger.info("✅ No trades needed - portfolio already at target")

            # Step 6: Record performance metrics
            logger.info("📊 Recording performance metrics...")
            execution_summary = self.strategy_manager.get_execution_summary(strategy_results)

            # Record individual strategy performance
            for strategy_name, result in strategy_results.items():
                self.performance_tracker.record_strategy_performance(
                    strategy_name, result, combined_positions
                )

            # Record combined portfolio performance
            self.performance_tracker.record_portfolio_performance(
                combined_positions, strategy_results, execution_summary
            )

            # Update rebalance time
            self.state_manager.set_last_rebalance(datetime.now(timezone.utc))
            self.state_manager.save_state()

            # Log performance summary
            self.performance_monitor.end_timer(timer_id)
            self.data_cache.log_stats()

            # Log final summary
            total_positions = len(combined_positions)
            total_capital = sum(pos.size_usd for pos in combined_positions)
            successful_strategies = len([r for r in strategy_results.values() if r.success])

            logger.info("✅ Multi-strategy portfolio rebalancing completed successfully")
            logger.info(f"📊 Final Summary:")
            logger.info(f"   Strategies executed: {successful_strategies}/{len(strategy_results)}")
            logger.info(f"   Combined positions: {total_positions}")
            logger.info(f"   Total capital: ${total_capital:,.0f}")

        except Exception as e:
            logger.error(f"❌ Multi-strategy rebalancing failed: {e}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            raise

    async def _prefetch_market_data(self):
        """Pre-fetch and cache market data that will be used by all strategies"""
        try:
            # Get eligible coins (this will cache the data for all strategies to use)
            eligible_coins = await self.data_fetcher.get_eligible_coins()
            logger.info(f"📡 Pre-fetched data for {len(eligible_coins)} eligible coins")

            # Pre-fetch OHLCV data for volatility calculations
            # This is done in batches by the data fetcher automatically

        except Exception as e:
            logger.warning(f"⚠️ Failed to pre-fetch some market data: {e}")
            # Don't raise - strategies can still fetch data individually

    def _convert_to_execution_format(self, combined_positions) -> List[Dict[str, Any]]:
        """Convert combined positions to format expected by executor"""
        execution_positions = []

        for pos in combined_positions:
            execution_pos = {
                'symbol': pos.symbol,
                'side': pos.side,
                'size_usd': pos.size_usd,
                'size_native': pos.size_native,
                'weight': pos.net_weight,
                'volatility': pos.metadata.get('volatility', 0.0),
                'leverage': pos.metadata.get('leverage', 1.0),
                'adjusted_funding': pos.metadata.get('adjusted_funding', 0.0),
                # Add multi-strategy metadata
                'contributing_strategies': pos.contributing_strategies,
                'strategy_contributions': pos.strategy_contributions,
                'confidence': pos.confidence
            }
            execution_positions.append(execution_pos)

        return execution_positions

    def _update_state_with_combined_positions(self, combined_positions):
        """Update state manager with combined portfolio positions"""
        # Convert to format expected by state manager
        state_positions = {}
        for pos in combined_positions:
            state_positions[pos.symbol] = {
                'symbol': pos.symbol,
                'side': pos.side,
                'size_usd': pos.size_usd,
                'size_native': pos.size_native,
                'weight': pos.net_weight,
                'contributing_strategies': pos.contributing_strategies,
                'strategy_contributions': pos.strategy_contributions
            }

        self.state_manager.set_positions(state_positions)

    async def _monitor_positions(self):
        """Monitor current positions from exchange"""
        try:
            timer_id = self.performance_monitor.start_timer("monitoring")

            logger.info("👁️ Starting position monitoring...")

            # Fetch current positions from exchange
            try:
                positions = await self.exchange.fetch_positions()
                active_positions = [pos for pos in positions if pos.get('contracts', 0) != 0]

                if active_positions:
                    logger.info(f"📊 Found {len(active_positions)} active positions:")
                    for pos in active_positions:
                        symbol = pos['symbol']
                        size = pos['contracts']
                        side = pos['side']
                        logger.info(f"   {symbol}: {side} {size:.6f}")
                else:
                    logger.info("📊 No active positions found")

            except Exception as e:
                logger.warning(f"⚠️ Failed to fetch positions: {e}")

            # Update monitoring time
            self.state_manager.set_last_monitoring_check(datetime.now(timezone.utc))
            self.state_manager.save_state()

            self.performance_monitor.end_timer(timer_id)
            logger.info("✅ Position monitoring completed")

        except Exception as e:
            logger.error(f"❌ Monitoring failed: {e}")
            import traceback
            logger.debug(f"Full monitoring error traceback: {traceback.format_exc()}")
            # Don't raise - monitoring failures shouldn't stop the orchestrator

    async def _cleanup(self):
        """Cleanup resources and save final state"""
        try:
            logger.info("🧹 Cleaning up orchestrator resources...")

            # Cancel any open orders if executor is available
            if self.executor:
                try:
                    logger.info("🚫 Canceling any remaining open orders...")
                    await self.executor._cancel_all_open_orders()
                except Exception as e:
                    logger.warning(f"⚠️ Failed to cancel orders during cleanup: {e}")

            # Save final state
            if self.state_manager:
                try:
                    logger.info("💾 Saving final state...")
                    self.state_manager.save_state()
                except Exception as e:
                    logger.error(f"❌ Failed to save final state: {e}")

            # Export performance data
            if self.performance_tracker:
                try:
                    logger.info("📤 Exporting performance data...")
                    export_file = self.performance_tracker.export_performance_data()
                    logger.info(f"📤 Performance data exported to: {export_file}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to export performance data: {e}")

            # Log performance summary
            if self.performance_monitor:
                try:
                    self.performance_monitor.log_summary()
                except Exception as e:
                    logger.warning(f"⚠️ Failed to log performance summary: {e}")

            # Close exchange connection
            if self.exchange:
                try:
                    logger.info("🔌 Closing exchange connection...")
                    if hasattr(self.exchange, 'close'):
                        await self.exchange.close()
                except Exception as e:
                    logger.warning(f"⚠️ Failed to close exchange connection: {e}")

            logger.info("✅ Orchestrator cleanup completed")

        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")

    def stop(self):
        """Stop the orchestrator gracefully"""
        logger.info("🛑 Stopping multi-strategy orchestrator...")
        self.running = False

    def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get current status of the orchestrator and all strategies"""
        registered_strategies = self.strategy_manager.get_registered_strategies()
        strategy_weights = self.strategy_manager.get_strategy_weights()

        status = {
            'running': self.running,
            'exchange': self.config.get('exchange', 'unknown'),
            'total_strategies': len(registered_strategies),
            'registered_strategies': registered_strategies,
            'strategy_weights': strategy_weights,
            'last_rebalance': self.state_manager.get_last_rebalance(),
            'data_cache_stats': self.data_cache.get_stats() if self.data_cache else {},
            'performance_summary': {
                'portfolio': self.performance_tracker.get_portfolio_performance_summary(7),  # Last 7 days
                'strategies': {
                    name: self.performance_tracker.get_strategy_performance_summary(name, 7)
                    for name in registered_strategies
                }
            }
        }

        return status
