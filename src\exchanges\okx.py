"""
OKX exchange implementation for StatArb Carry Trade Strategy
"""

import ccxt.async_support as ccxt
from typing import Dict, List, Optional, Any
from utils import get_logger
from exchanges.base import ExchangeInterface

logger = get_logger(__name__)


class OKXExchange(ExchangeInterface):
    """OKX exchange implementation"""

    def __init__(self):
        self.exchange = None
        self._name = "OKX"

    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize OKX exchange connection with error handling"""
        try:
            exchange_options = {
                'defaultType': 'swap',  # Use swap for perpetual futures
                'recvWindow': 20000,
                'adjustForTimeDifference': True,
            }

            api_key = config.get('api_key', '').strip()
            api_secret = config.get('api_secret', '').strip()
            passphrase = config.get('passphrase', '').strip()

            # Prepare constructor parameters
            constructor_params = {
                'enableRateLimit': True,
                'options': exchange_options,
            }

            # Add API keys if provided
            if api_key and api_secret and passphrase:
                constructor_params['apiKey'] = api_key
                constructor_params['secret'] = api_secret
                constructor_params['password'] = passphrase  # OKX requires passphrase
                logger.info("🔑 Using API keys for private endpoints")
            else:
                logger.info("🌐 Using public endpoints only (no API keys provided)")

            self.exchange = ccxt.okx(constructor_params)

            # Handle different trading modes
            if config.get('use_demo', False):
                # OKX demo trading
                if hasattr(self.exchange, 'enable_demo_trading'):
                    self.exchange.enable_demo_trading(True)
                    logger.info("🎭 OKX demo trading mode enabled via CCXT")
                else:
                    # Fallback: manually set demo URLs
                    logger.info("🔧 Setting demo URLs manually for older CCXT version")
                    self.exchange.urls['api'] = {
                        'rest': 'https://www.okx.com',  # OKX demo endpoint
                        'ws': 'wss://ws.okx.com:8443/ws/v5/public',
                    }
                    logger.info("🎭 OKX demo trading mode enabled via manual URL override")
            elif config.get('use_testnet', False):
                self.exchange.set_sandbox_mode(True)
                logger.info("🧪 OKX testnet/sandbox mode enabled")

            # Test public endpoints first
            try:
                markets = await self.fetch_markets()

                # Determine mode for logging
                if config.get('use_demo', False):
                    mode = "demo"
                elif config.get('use_testnet', False):
                    mode = "testnet"
                else:
                    mode = "live"

                logger.info(f"✅ Connected to {self._name} {mode}")
                logger.info(f"✅ Found {len(markets)} markets")

                # Test a public ticker to ensure connectivity
                if markets:
                    # Try to find a common symbol that's likely to have data
                    test_symbols = ['BTC/USDT:USDT', 'BTC/USDT', 'ETH/USDT:USDT', 'ETH/USDT']
                    test_symbol = None

                    for symbol in test_symbols:
                        if symbol in markets:
                            test_symbol = symbol
                            break

                    if not test_symbol:
                        # Fallback to first available symbol
                        test_symbol = list(markets.keys())[0]

                    try:
                        ticker = await self.fetch_ticker(test_symbol)
                        logger.info(f"✅ Public API test successful with {test_symbol}")
                    except Exception as e:
                        logger.warning(f"⚠️ Ticker test failed for {test_symbol}: {e}")
                        logger.info("✅ Markets loaded successfully, continuing without ticker test")

                # Test private endpoints if credentials provided
                if api_key and api_secret and passphrase:
                    try:
                        await self.fetch_balance()
                        logger.info("✅ Private API test successful")
                    except Exception as e:
                        logger.warning(f"⚠️ Private API test failed: {e}")
                        logger.info("🌐 Continuing with public endpoints only")

                return True

            except Exception as e:
                logger.error(f"❌ Failed to test {self._name} connection: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to initialize {self._name} exchange: {e}")
            return False

    async def fetch_markets(self) -> Dict[str, Any]:
        """Fetch available markets"""
        try:
            markets = await self.exchange.load_markets()
            return markets
        except Exception as e:
            logger.error(f"❌ Failed to fetch markets from {self._name}: {e}")
            raise

    async def fetch_tickers(self) -> Dict[str, Any]:
        """Fetch ticker data for all symbols"""
        try:
            tickers = await self.exchange.fetch_tickers()
            return tickers
        except Exception as e:
            logger.error(f"❌ Failed to fetch tickers from {self._name}: {e}")
            raise

    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """Fetch ticker data for a single symbol"""
        try:
            ticker = await self.exchange.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            logger.error(f"❌ Failed to fetch ticker for {symbol} from {self._name}: {e}")
            raise

    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List[List]:
        """Fetch OHLCV data"""
        try:
            ohlcv = await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            return ohlcv
        except Exception as e:
            logger.error(f"❌ Failed to fetch OHLCV for {symbol} from {self._name}: {e}")
            raise

    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """Fetch current funding rate"""
        try:
            funding_rate = await self.exchange.fetch_funding_rate(symbol)
            return funding_rate
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate for {symbol} from {self._name}: {e}")
            raise

    async def fetch_funding_rate_history(self, symbol: str, limit: int) -> List[Dict]:
        """Fetch funding rate history"""
        try:
            history = await self.exchange.fetch_funding_rate_history(symbol, limit=limit)
            return history
        except Exception as e:
            logger.error(f"❌ Failed to fetch funding rate history for {symbol} from {self._name}: {e}")
            raise

    async def fetch_balance(self) -> Dict[str, Any]:
        """Fetch account balance"""
        try:
            balance = await self.exchange.fetch_balance()
            return balance
        except Exception as e:
            logger.error(f"❌ Failed to fetch balance from {self._name}: {e}")
            raise

    async def fetch_positions(self, symbols: Optional[List[str]] = None) -> List[Dict]:
        """Fetch positions"""
        try:
            positions = await self.exchange.fetch_positions(symbols)
            return positions
        except Exception as e:
            logger.error(f"❌ Failed to fetch positions from {self._name}: {e}")
            raise

    async def fetch_open_orders(self, symbol: str) -> List[Dict]:
        """Fetch open orders"""
        try:
            orders = await self.exchange.fetch_open_orders(symbol)
            return orders
        except Exception as e:
            logger.error(f"❌ Failed to fetch open orders for {symbol} from {self._name}: {e}")
            raise

    async def fetch_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Fetch order status"""
        try:
            order = await self.exchange.fetch_order(order_id, symbol)
            return order
        except Exception as e:
            logger.error(f"❌ Failed to fetch order {order_id} for {symbol} from {self._name}: {e}")
            raise

    async def fetch_order_book(self, symbol: str) -> Dict[str, Any]:
        """Fetch order book"""
        try:
            order_book = await self.exchange.fetch_order_book(symbol)
            return order_book
        except Exception as e:
            logger.error(f"❌ Failed to fetch order book for {symbol} from {self._name}: {e}")
            raise

    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict[str, Any]:
        """Create limit order"""
        try:
            order = await self.exchange.create_limit_order(symbol, side, amount, price, params)
            return order
        except Exception as e:
            logger.error(f"❌ Failed to create limit order for {symbol} on {self._name}: {e}")
            raise

    async def create_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict[str, Any]:
        """Create market order"""
        try:
            order = await self.exchange.create_market_order(symbol, side, amount, params)
            return order
        except Exception as e:
            logger.error(f"❌ Failed to create market order for {symbol} on {self._name}: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Cancel order"""
        try:
            result = await self.exchange.cancel_order(order_id, symbol)
            return result
        except Exception as e:
            logger.error(f"❌ Failed to cancel order {order_id} for {symbol} on {self._name}: {e}")
            raise

    @property
    def name(self) -> str:
        """Exchange name"""
        return self._name
