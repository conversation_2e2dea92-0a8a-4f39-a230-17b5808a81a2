#!/usr/bin/env python3

"""
Comprehensive test suite for multi-strategy trading system

This test suite validates:
1. Strategy framework components
2. Portfolio combination logic
3. Performance tracking
4. Configuration handling
5. Error handling and edge cases
"""

import asyncio
import sys
import os
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Add src to Python path
src_path = Path(__file__).parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Import test modules
try:
    from config import Config
    from strategies.base import BaseStrategy, StrategyPosition, StrategyResult
    from strategies import StrategyManager
    from strategies.stat_arb_carry_trade import StatArbCarryTradeStrategy
    from portfolio import PortfolioCombiner, PerformanceTracker
    print("✅ All imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class MockDataFetcher:
    """Mock data fetcher for testing"""
    
    async def get_eligible_coins(self):
        return [
            {'symbol': 'BTCUSDT', 'avg_volume_5d_usdt': 1000000000, 'adjusted_funding': 0.001},
            {'symbol': 'ETHUSDT', 'avg_volume_5d_usdt': 500000000, 'adjusted_funding': -0.0005},
            {'symbol': 'ADAUSDT', 'avg_volume_5d_usdt': 100000000, 'adjusted_funding': 0.0008}
        ]
    
    async def get_cached_ohlcv(self, symbol, timeframe, limit):
        # Return mock OHLCV data
        return [[1640995200000, 50000, 51000, 49000, 50500, 1000] for _ in range(limit)]


class MockDataAnalyzer:
    """Mock data analyzer for testing"""
    
    async def enrich_coins_with_volatility(self, coins):
        for coin in coins:
            coin['weighted_volatility'] = 0.3
            coin['beta_60d'] = 1.0
        return coins
    
    def select_positions_ev_based(self, enriched_symbols, current_positions):
        # Simple mock selection
        longs = [s for s in enriched_symbols if s.get('adjusted_funding', 0) < 0][:2]
        shorts = [s for s in enriched_symbols if s.get('adjusted_funding', 0) > 0][:2]
        return longs, shorts


class MockExchange:
    """Mock exchange for testing"""
    
    async def fetch_markets(self):
        return {
            'BTCUSDT': {'symbol': 'BTCUSDT', 'type': 'swap', 'quote': 'USDT', 'active': True},
            'ETHUSDT': {'symbol': 'ETHUSDT', 'type': 'swap', 'quote': 'USDT', 'active': True},
            'ADAUSDT': {'symbol': 'ADAUSDT', 'type': 'swap', 'quote': 'USDT', 'active': True}
        }
    
    async def fetch_ticker(self, symbol):
        return {'symbol': symbol, 'quoteVolume': 1000000}
    
    async def fetch_positions(self):
        return []


class TestStrategy(BaseStrategy):
    """Test strategy for validation"""
    
    def __init__(self, strategy_name: str, positions_to_return: list):
        config = {'test_param': 1.0}
        super().__init__(strategy_name, config, None, None, None)
        self.positions_to_return = positions_to_return
    
    def _validate_config(self) -> None:
        pass
    
    async def get_universe(self) -> list:
        return ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
    
    async def calculate_features(self, symbols: list) -> list:
        return [{'symbol': symbol, 'feature': 1.0} for symbol in symbols]
    
    async def select_positions(self, enriched_symbols: list) -> tuple:
        return enriched_symbols[:2], enriched_symbols[2:]
    
    async def size_positions(self, long_candidates: list, short_candidates: list) -> list:
        return self.positions_to_return


async def test_strategy_manager():
    """Test strategy manager functionality"""
    print("\n🧪 Testing Strategy Manager...")
    
    config = {
        'max_concurrent_strategies': 2,
        'strategy_timeout_seconds': 30
    }
    
    manager = StrategyManager(config)
    
    # Create test strategies
    strategy1_positions = [
        StrategyPosition('BTCUSDT', 'long', 1000.0, 0.1, 0.5),
        StrategyPosition('ETHUSDT', 'short', 500.0, 0.2, 0.3)
    ]
    
    strategy2_positions = [
        StrategyPosition('BTCUSDT', 'short', 300.0, 0.05, 0.4),
        StrategyPosition('ADAUSDT', 'long', 200.0, 100.0, 0.2)
    ]
    
    strategy1 = TestStrategy('test_strategy_1', strategy1_positions)
    strategy2 = TestStrategy('test_strategy_2', strategy2_positions)
    
    # Register strategies
    manager.register_strategy(strategy1, 0.7)
    manager.register_strategy(strategy2, 0.3)
    
    # Validate
    assert manager.validate_strategies(), "Strategy validation failed"
    assert len(manager.get_registered_strategies()) == 2, "Wrong number of registered strategies"
    
    # Execute strategies
    results = await manager.execute_all_strategies()
    assert len(results) == 2, "Wrong number of execution results"
    
    for name, result in results.items():
        assert result.success, f"Strategy {name} execution failed"
        assert result.position_count > 0, f"Strategy {name} returned no positions"
    
    print("✅ Strategy Manager tests passed")
    return True


async def test_portfolio_combiner():
    """Test portfolio combination functionality"""
    print("\n🧪 Testing Portfolio Combiner...")
    
    config = {
        'min_position_size_usd': 10.0,
        'enable_position_netting': True
    }
    
    combiner = PortfolioCombiner(config)
    
    # Create test strategy results
    strategy1_positions = [
        StrategyPosition('BTCUSDT', 'long', 1000.0, 0.1, 0.5),
        StrategyPosition('ETHUSDT', 'short', 500.0, 0.2, 0.3)
    ]
    
    strategy2_positions = [
        StrategyPosition('BTCUSDT', 'short', 300.0, 0.05, 0.4),  # Opposite to strategy1
        StrategyPosition('ADAUSDT', 'long', 200.0, 100.0, 0.2)
    ]
    
    result1 = StrategyResult(
        strategy_name='strategy1',
        target_positions=strategy1_positions,
        total_capital_allocated=1500.0,
        execution_time_seconds=1.0,
        universe_size=3,
        candidates_count=2,
        success=True
    )
    
    result2 = StrategyResult(
        strategy_name='strategy2',
        target_positions=strategy2_positions,
        total_capital_allocated=500.0,
        execution_time_seconds=0.8,
        universe_size=3,
        candidates_count=2,
        success=True
    )
    
    strategy_results = {'strategy1': result1, 'strategy2': result2}
    strategy_weights = {'strategy1': 0.7, 'strategy2': 0.3}
    
    # Combine portfolios
    combined_positions = combiner.combine_portfolios(strategy_results, strategy_weights)
    
    # Validate results
    assert len(combined_positions) > 0, "No combined positions generated"
    
    # Check that BTCUSDT positions were netted (1000*0.7 - 300*0.3 = 610)
    btc_positions = [pos for pos in combined_positions if pos.symbol == 'BTCUSDT']
    assert len(btc_positions) == 1, "BTCUSDT positions not properly netted"
    assert btc_positions[0].side == 'long', "BTCUSDT net position should be long"
    assert abs(btc_positions[0].size_usd - 610.0) < 1.0, "BTCUSDT position size incorrect"
    
    print("✅ Portfolio Combiner tests passed")
    return True


async def test_performance_tracker():
    """Test performance tracking functionality"""
    print("\n🧪 Testing Performance Tracker...")
    
    # Create temporary file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_file = f.name
    
    try:
        config = {
            'max_performance_history_days': 30,
            'enable_detailed_performance_tracking': True,
            'performance_tracking_file': temp_file
        }
        
        tracker = PerformanceTracker(config)
        
        # Create test strategy result
        strategy_positions = [
            StrategyPosition('BTCUSDT', 'long', 1000.0, 0.1, 0.5)
        ]
        
        result = StrategyResult(
            strategy_name='test_strategy',
            target_positions=strategy_positions,
            total_capital_allocated=1000.0,
            execution_time_seconds=2.5,
            universe_size=10,
            candidates_count=5,
            success=True
        )
        
        # Record performance
        tracker.record_strategy_performance('test_strategy', result, [])
        
        # Get performance summary
        summary = tracker.get_strategy_performance_summary('test_strategy', 7)
        
        assert summary is not None, "No performance summary available"
        assert summary['total_executions'] == 1, "Wrong execution count"
        assert summary['success_rate'] == 1.0, "Wrong success rate"
        
        print("✅ Performance Tracker tests passed")
        return True
        
    finally:
        # Clean up
        try:
            os.unlink(temp_file)
        except FileNotFoundError:
            pass


async def test_stat_arb_strategy_integration():
    """Test StatArb strategy integration with mocks"""
    print("\n🧪 Testing StatArb Strategy Integration...")
    
    # Create mock config
    main_config = {
        'total_capital_usd': 10000,
        'exchange': 'bybit',
        'target_volatility': 0.3,
        'enable_beta_projection': True
    }
    
    # Create mocks
    mock_data_fetcher = MockDataFetcher()
    mock_data_analyzer = MockDataAnalyzer()
    mock_exchange = MockExchange()
    
    try:
        # Initialize strategy
        strategy = StatArbCarryTradeStrategy(
            data_fetcher=mock_data_fetcher,
            data_analyzer=mock_data_analyzer,
            exchange=mock_exchange,
            main_config=main_config
        )
        
        # Test strategy info
        info = strategy.get_strategy_info()
        assert info['name'] == 'stat_arb_carry_trade', "Wrong strategy name"
        assert info['description'] == 'Statistical Arbitrage Carry Trade Strategy', "Wrong description"
        
        # Test health validation
        health = strategy.validate_strategy_health()
        assert health['overall_health'] in ['healthy', 'warning'], "Strategy health check failed"
        
        print("✅ StatArb Strategy Integration tests passed")
        return True
        
    except Exception as e:
        print(f"❌ StatArb Strategy Integration test failed: {e}")
        return False


async def test_configuration_handling():
    """Test configuration loading and validation"""
    print("\n🧪 Testing Configuration Handling...")
    
    # Test config creation
    test_config_data = {
        'strategies': {
            'stat_arb_carry_trade': {
                'enabled': True,
                'weight': 1.0
            }
        },
        'total_capital_usd': 10000,
        'exchange': 'bybit',
        'portfolio_combination': {
            'enable_position_netting': True,
            'min_position_size_usd': 10.0
        }
    }
    
    config = Config(test_config_data)
    
    # Test config access
    assert config.get('total_capital_usd') == 10000, "Config value access failed"
    assert config.get('strategies', {}).get('stat_arb_carry_trade', {}).get('enabled') == True, "Nested config access failed"
    
    # Test strategy weights extraction
    strategies_config = config.get('strategies', {})
    enabled_strategies = [name for name, cfg in strategies_config.items() if cfg.get('enabled', True)]
    assert len(enabled_strategies) == 1, "Wrong number of enabled strategies"
    assert 'stat_arb_carry_trade' in enabled_strategies, "StatArb strategy not enabled"
    
    print("✅ Configuration Handling tests passed")
    return True


async def run_all_tests():
    """Run all test suites"""
    print("🚀 Starting comprehensive multi-strategy system tests...")
    
    tests = [
        test_strategy_manager,
        test_portfolio_combiner,
        test_performance_tracker,
        test_stat_arb_strategy_integration,
        test_configuration_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if await test():
                passed += 1
                print(f"✅ {test.__name__} passed")
            else:
                failed += 1
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} failed with exception: {e}")
            import traceback
            print(f"   Traceback: {traceback.format_exc()}")
    
    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Multi-strategy system is working correctly.")
        return True
    else:
        print("💥 Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
