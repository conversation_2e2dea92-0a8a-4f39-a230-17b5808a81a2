"""
Configuration management with secure credential handling
"""

import os
import json
import yaml
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class Config:
    """Configuration manager with environment variable support"""
    
    def __init__(self, config_data: Dict[str, Any]):
        self._config = config_data
        self._apply_environment_overrides()
    
    def _apply_environment_overrides(self):
        """Apply environment variable overrides for sensitive data"""
        env_mappings = {
            'BYBIT_API_KEY': ['api_key'],
            'BYBIT_API_SECRET': ['api_secret'],
            'BINANCE_API_KEY': ['api_key'],
            'BINANCE_API_SECRET': ['api_secret'],
            'OKX_API_KEY': ['api_key'],
            'OKX_API_SECRET': ['api_secret'],
            'OKX_PASSPHRASE': ['passphrase'],
            'HYPERLIQUID_WALLET_ADDRESS': ['wallet_address'],
            'HYPERLIQUID_PRIVATE_KEY': ['private_key'],
            'TOTAL_CAPITAL': ['total_capital_usd'],
            'USE_TESTNET': ['use_testnet'],
            'USE_DEMO': ['use_demo'],
            'SIMULATION_MODE': ['simulation_mode'],
            'EXCHANGE': ['exchange'],
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Convert string values to appropriate types
                if env_var in ['USE_TESTNET', 'USE_DEMO', 'SIMULATION_MODE']:
                    env_value = env_value.lower() in ('true', '1', 'yes')
                elif env_var == 'TOTAL_CAPITAL':
                    env_value = float(env_value)
                
                # Set the config value
                current = self._config
                for key in config_path[:-1]:
                    current = current.setdefault(key, {})
                current[config_path[-1]] = env_value
                
                logger.info(f"🔧 Applied environment override for {config_path[-1]}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with dot notation support"""
        keys = key.split('.')
        current = self._config
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def set(self, key: str, value: Any):
        """Set configuration value with dot notation support"""
        keys = key.split('.')
        current = self._config
        
        for k in keys[:-1]:
            current = current.setdefault(k, {})
        
        current[keys[-1]] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Return configuration as dictionary"""
        return self._config.copy()
    
    def get_exchange_config(self, exchange_name: str) -> Dict[str, Any]:
        """Get exchange-specific configuration"""
        base_config = {
            'api_key': self.get('api_key', ''),
            'api_secret': self.get('api_secret', ''),
            'use_testnet': self.get('use_testnet', False),
            'use_demo': self.get('use_demo', False),
        }
        
        # Add exchange-specific overrides
        exchange_config = self.get(f'{exchange_name.lower()}_config', {})
        base_config.update(exchange_config)
        
        return base_config


def load_config(config_path: Optional[str] = None) -> Config:
    """Load configuration from file or use defaults"""
    
    # Default configuration
    default_config = {
        # Exchange settings
        'exchange': 'bybit',
        'api_key': '',
        'api_secret': '',
        'use_testnet': True,
        'use_demo': False,

        # Strategy parameters
        'total_capital_usd': 10000,
        'min_daily_volume_usd': 1000000,
        'max_abs_funding_rate': 0.0030,
        'min_volatility_threshold': 0.05,

        # EV-Based Position Selection
        'peak_abs_funding_rate': 0.001,
        'max_position_capital_pct': 25,
        'min_positions_per_leg': 2,

        'trading_cost_adjustment': {
            'bybit': 0.0001,
            'binance': 0.0001,
            'okx': 0.0001,
            'hyperliquid': 0.0000125
        },
        
        # Risk management
        'target_volatility': 0.30,  # Standardized to match config.yaml
        'min_leverage': 0.05,       # Standardized to match config.yaml
        'max_leverage': 5.0,
        'max_position_size_usd': None,
        'min_orderbook_depth': 3,
        'max_spread_threshold': 0.05,

        # Buffer zone and delta tolerance
        'buffer_zone_tolerance_percentage': 5.0,
        'delta_tolerance_percentage': 0.5,
        'min_close_threshold_usd': 10,
        
        # Execution settings
        'min_batch_size': 1,
        'max_batch_size': 5,
        'min_batch_interval_seconds': 30,
        'max_batch_interval_seconds': 300,
        'min_orderbook_levels': 5,
        'max_orderbook_levels': 10,
        'max_order_monitoring_seconds': 1800,
        'max_fetch_retries_per_order': 5,
        
        # Performance settings
        'max_concurrent_api_calls': 10,
        'rate_limit_delay_ms': 100,
        'cache_ttl_seconds': 300,
        'circuit_breaker_threshold': 5,
        'circuit_breaker_timeout': 60,
        'symbol_processing_batch_size': 50,
        'cache_cleanup_interval': 600,
        'order_history_retention_days': 7,

        # Data analysis
        'default_volatility': 0.20,  # Standardized to match config.yaml defaults

        # Timing configuration
        'max_sleep_seconds': 3600,
        'default_sleep_seconds': 3600,
        'monitoring_window_seconds': 300,
        'shutdown_timeout_seconds': 60,

        # Cache configuration
        'cache_default_ttl': 300,
        'cache_max_size': 1000,
        'cache_gc_interval': 300,

        # Control flags
        'immediate_start': True,
        'simulation_mode': True,
        'debug_mode': False,
    }
    
    # Try to load from file if specified
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    file_config = yaml.safe_load(f)
                else:
                    file_config = json.load(f)
            
            # Merge with defaults
            default_config.update(file_config)
            logger.info(f"📁 Loaded configuration from {config_path}")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to load config from {config_path}: {e}")
            logger.info("🔧 Using default configuration")
    
    else:
        logger.info("🔧 Using default configuration")
    
    return Config(default_config)
