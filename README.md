# StatArb Carry Trade Strategy

A sophisticated funding arbitrage strategy for cryptocurrency perpetual futures that exploits funding rate differentials while maintaining market-neutral positions. The strategy uses mean funding rates, volatility-targeted position sizing, and intelligent cost adjustment to maximize returns while minimizing risk.

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Settings
Edit `config.yaml`:
```yaml
exchange: bybit
api_key: ""
api_secret: ""
use_testnet: true
total_capital_usd: 10000
peak_abs_funding_rate: 0.001      # EV-based selection
max_position_capital_pct: 25      # Max 25% per position
min_positions_per_leg: 2          # Min 2 positions per leg
simulation_mode: true
```

### 3. Run the Strategy
```bash
python main.py
```

## Features

### Core Strategy Features
- **Multi-exchange support**: Bybit, Binance, OKX, Hyperliquid with unified interface
- **EV-based position selection**: Sigmoid weighting function for optimal capital allocation
- **Market-neutral construction**: Equal long/short exposure with dollar volatility balancing
- **Mean funding rate analysis**: Uses 3-day mean funding rates for better signal quality
- **Intelligent cost adjustment**: Selective cost adjustment for new vs. held positions
- **Volatility-targeted sizing**: Dynamic position sizing based on weighted volatility metrics
- **Buffer zone trading**: Over-trading prevention with configurable tolerance zones

### Execution & Risk Management
- **Randomized batch execution**: Reduces market impact with fresh orderbook data
- **Post-only order optimization**: Minimizes trading costs with retry logic
- **Real-time monitoring**: Continuous position tracking and alignment checking
- **Exchange-specific optimization**: Tailored trading costs and execution parameters
- **Comprehensive state management**: Persistent strategy state across restarts

## Project Structure

```
funding_arb/
├── main.py                 # Entry point
├── config.yaml            # Configuration file
├── requirements.txt        # Dependencies
├── setup.py               # Setup script
├── logs/                  # Log files
└── src/                   # Source code
    ├── config/            # Configuration management
    ├── core/              # Strategy implementation
    ├── data/              # Data fetching and analysis
    ├── exchanges/         # Exchange interfaces
    ├── execution/         # Order execution
    ├── storage/           # State management
    └── utils/             # Utility functions
```

## Configuration

### Basic Settings
```yaml
# Exchange selection
exchange: bybit                    # bybit, binance, okx, hyperliquid
use_testnet: true                 # Use testnet environment
use_demo: false                   # Use demo account (if available)

# Capital allocation
total_capital_usd: 10000          # Total capital to deploy

# EV-Based Position Selection
peak_abs_funding_rate: 0.001      # Peak funding rate for sigmoid weighting (0.1%)
max_position_capital_pct: 25      # Maximum capital per position (25%)
min_positions_per_leg: 2          # Minimum positions per leg for diversification

# Market filters
min_daily_volume_usd: 1000000     # Minimum 5-day mean volume filter ($1M)
max_abs_funding_rate: 0.01        # Maximum absolute funding rate (±1%)
min_volatility_threshold: 0.05    # Minimum volatility to exclude stablecoins (5%)
exclude_new_listings_days: 60     # Exclude coins listed within 60 days

# Trading costs (exchange-specific with floor protection)
trading_cost_adjustment:
  bybit: 0.0001                   # 0.01% for Bybit
  binance: 0.0001                 # 0.01% for Binance
  okx: 0.0001                     # 0.01% for OKX
  hyperliquid: 0.0000125          # 0.00125% for Hyperliquid (lower fees)

# Timing configuration
max_sleep_seconds: 3600           # Maximum sleep duration between checks (1 hour)
default_sleep_seconds: 3600       # Default sleep when no events scheduled (1 hour)
monitoring_window_seconds: 300    # Window around monitoring time (5 minutes)

# Cache Configuration (Memory Management)
cache_default_ttl: 300            # Default cache TTL in seconds (5 minutes)
cache_max_size: 1000              # Maximum cache entries
cache_gc_interval: 300            # Garbage collection interval in seconds (5 minutes)
```

### Credential Setup
```yaml
# Standard exchanges (Bybit, Binance)
api_key: "your_api_key"
api_secret: "your_api_secret"

# OKX (requires passphrase)
passphrase: "your_passphrase"

# Hyperliquid (uses wallet)
wallet_address: "your_wallet_address"
private_key: "your_private_key"
```

## Strategy Logic

The strategy operates by:

1. **Market Analysis**: Fetches funding rates and volume data from the selected exchange
2. **Filtering**: Applies 5-day mean volume and funding rate filters to identify eligible markets
3. **Position Selection**: Selects equal numbers of long and short positions based on funding rates
4. **Order Execution**: Places limit orders with randomized timing and pricing
5. **Monitoring**: Tracks positions and rebalances at scheduled intervals

### EV-Based Position Selection Process
- **Funding Rate Analysis**: Calculates 3-day mean funding rates for all eligible markets
- **Intelligent Cost Adjustment**:
  - New positions: Apply trading cost adjustment with floor protection
  - Held positions: Use raw funding rates (no cost penalty)
  - Floor protection: Positive rates can't go negative, negative rates can't go positive
- **EV-Based Market Selection**:
  - **No Fixed Position Counts**: Selects all coins with positive Expected Value (non-zero adjusted funding)
  - **Sigmoid Weighting**: Uses `x * exp(-(x/peak)²)` function for position allocation based on funding rates
  - **Long positions**: Negative funding rates = positive EV (we get paid to long)
  - **Short positions**: Positive funding rates = positive EV (we get paid to short)
- **Portfolio Construction**:
  - **Equal Dollar Volatility**: Maintains equal dollar volatility between long and short legs
  - **Flexible Position Counts**: Allows different numbers of longs vs shorts based on market opportunities
  - **Maximum Capital Constraint**: Limits individual positions to 25% of total capital
  - **Minimum Leg Size**: Ensures at least 2 positions per leg for diversification
- **Volume Filtering**: Uses 5-day mean volume above minimum threshold
- **Volatility Screening**: Excludes low-volatility assets (likely stablecoins)

### Order Execution
- **Buffer Zone Trading** (Prevents Over-Trading):
  - Creates buffer zones around target positions (configurable % of position size)
  - **No-Trade Zone**: If current position is within buffer, no trades executed
  - **Smart Rebalancing**: Only trades to buffer edges, not exact targets
  - **Example**: 5% buffer on $1000 target = trade only if position outside $950-$1050 range
- **Randomized Batch Execution**:
  - Divides orders into 3-5 random batches per coin
  - Random timing intervals (60-300 seconds between batches)
  - Random delta allocation (15-35% per batch)
- **Smart Order Placement**:
  - Uses limit orders with post-only flag and fresh orderbook data
  - Places orders at random orderbook levels (3-7 levels deep)
  - Fetches fresh orderbook immediately before each order
- **Retry Logic & Reconciliation**:
  - Implements retry logic with fresh orderbook fetching for post-only rejections
  - Cancels all open orders between execution iterations for clean reconciliation
  - Continues until position delta is within 0.25% of target
- **Concurrent Execution**: Processes multiple coins simultaneously with rate limiting

## Environment Variables

Alternative to configuration file credentials:

```bash
# Bybit
export BYBIT_API_KEY="your_key"
export BYBIT_API_SECRET="your_secret"

# Binance
export BINANCE_API_KEY="your_key"
export BINANCE_API_SECRET="your_secret"

# OKX
export OKX_API_KEY="your_key"
export OKX_API_SECRET="your_secret"
export OKX_PASSPHRASE="your_passphrase"

# Hyperliquid
export HYPERLIQUID_WALLET_ADDRESS="your_address"
export HYPERLIQUID_PRIVATE_KEY="your_key"
```

## Usage

### Initial Setup
1. Configure `config.yaml` with your preferred settings
2. Set up API credentials (file or environment variables)
3. Start with testnet mode for testing

### Interactive Setup
If no credentials are found, the strategy will prompt for interactive setup:

```
🔧 Credential Setup
========================================
Exchange (bybit/binance/okx/hyperliquid) [bybit]: bybit
Bybit API Key: your_api_key
Bybit API Secret: your_api_secret
Use testnet? (y/n) [y]: y
Use demo trading? (y/n) [n]: n
Total capital USD [10000]: 5000
Enable simulation mode (no real trades)? (y/n) [y]: y
```

**Simulation Mode Options:**
- `y` (Yes): Paper trading only - no real orders placed
- `n` (No): Live trading - real orders will be executed

### Running the Strategy
```bash
python main.py
```

The system will:
- Load configuration and validate settings
- Connect to the specified exchange
- Analyze markets and select positions
- Execute orders and monitor performance (unless in simulation mode)
- Rebalance daily at 23:00 UTC and monitor at 23:00, 07:00, 15:00 UTC

### Checking Position Alignment
```bash
# Check if current positions match targets
python check_positions.py

# Check with custom tolerance
python check_positions.py --tolerance 0.01

# Verbose output
python check_positions.py --verbose
```

### Monitoring
- Check console output for real-time status
- Review logs in `logs/statarb_carry_trade.log`
- Monitor strategy state in `strategy_state.json`

## Key Components

### Data Fetcher (`src/data/fetcher.py`)
- Fetches market data and funding rates from exchanges
- Implements caching to reduce API calls
- Handles rate limiting and error recovery

### Data Analyzer (`src/data/analyzer.py`)
- Analyzes funding rates and volatility
- Selects optimal position pairs
- Calculates position sizes based on volatility

### Randomized Executor (`src/execution/randomized_executor.py`)
- Executes positions using randomized batch execution with fresh orderbook data
- Implements delta-based trading with post-only order optimization
- Provides concurrent execution across multiple coins with rate limiting
- Includes automatic position reconciliation and retry logic for post-only rejections

### Position Manager (`src/execution/position_manager.py`)
- Manages portfolio positions and calculates volatility-targeted sizing
- Calculates required trades for rebalancing
- Monitors position performance and risk metrics

### State Manager (`src/storage/state_manager.py`)
- Persists strategy timing state across restarts
- Tracks last rebalance and monitoring times
- Provides timing continuity for strategy scheduling

## Risk Management

The strategy implements comprehensive risk management features:

### Position Risk Controls
- **Market Neutrality**: Equal dollar volatility between long and short legs (not necessarily equal position counts)
- **Position Limits**: Maximum 25% of capital per individual position
- **EV-Based Selection**: Only trades coins with positive Expected Value (non-zero adjusted funding)
- **Sigmoid Risk Control**: Automatically reduces weights for extreme funding rates
- **Volatility-Targeted Sizing**: Dynamic sizing based on weighted volatility (30% × 60d + 50% × 30d + 20% × 10d)
- **Minimum Diversification**: Ensures at least 2 positions per leg
- **New Listing Protection**: Excludes coins listed within configurable days (default: 60 days)
- **Volume Filtering**: Requires minimum 5-day mean volume to ensure liquidity

### Cost Management
- **Exchange-Specific Costs**: Tailored trading cost adjustments per exchange
- **Floor Protection**: Cost adjustments cannot flip funding rate signs
- **Selective Adjustment**: Only new positions incur cost adjustment, held positions use raw rates

### Operational Risk Controls
- **State Persistence**: Strategy state saved across restarts
- **Error Handling**: Comprehensive exception handling with graceful degradation
- **Rate Limiting**: Adaptive API rate limiting to prevent exchange restrictions
- **Monitoring**: Continuous position tracking with alignment verification

### Recommended Practices
- **Testing**: Start with testnet environment for thorough testing
- **Capital Management**: Use conservative EV thresholds initially (lower peak_abs_funding_rate)
- **Performance Monitoring**: Track funding collection vs trading costs regularly
- **Log Review**: Monitor logs for system health and performance metrics
- **Gradual Scaling**: Increase position sizes gradually as confidence builds

## Troubleshooting

### Common Issues

**Connection Errors**
- Verify API credentials are correct
- Check network connectivity
- Ensure sufficient account balance

**No Eligible Markets**
- Adjust 5-day mean volume and funding rate filters
- Check current market conditions
- Verify exchange market availability

**Order Execution Issues**
- Check position limits on exchange
- Verify margin requirements
- Review orderbook liquidity

### Debug Information
- Enable debug mode in configuration
- Check logs in `logs/statarb_carry_trade.log`
- Monitor strategy state in `strategy_state.json`

## Technical Details

### Data Processing
- Fetches market data and funding rates from exchange APIs
- Implements caching to reduce redundant API calls
- Uses concurrent processing for improved performance
- Handles rate limiting and connection errors

### EV-Based Position Sizing
- **Sigmoid Weighting**: Uses `x * exp(-(x/peak)²)` function to allocate capital based on Expected Value
- **Volatility-Targeted Sizing**: Uses weighted volatility calculation (30% × 60d + 50% × 30d + 20% × 10d)
- **Dynamic Leverage**: Adjusts leverage based on target volatility vs. weighted volatility
- **Equal Dollar Volatility**: Balances long and short legs to have equal dollar volatility for market neutrality
- **Maximum Position Limits**: Caps individual positions at 25% of total capital
- **Cost-Aware Selection**: Applies intelligent cost adjustment with floor protection
- **Volume-Weighted Ranking**: Uses volume as tie-breaker for equal EV weights

### Order Management
- Places limit orders with post-only flag when supported
- Implements randomized timing to reduce market impact
- Uses retry logic for failed or partially filled orders
- Tracks order status and maintains persistent state

### Timing Schedule
- **Rebalancing**: Daily at 23:00 UTC (1 hour before 00:00 funding)
- **Monitoring**: At 23:00, 07:00, 15:00 UTC (1 hour before each funding time)
- **Funding Times**: 00:00, 08:00, 16:00 UTC (standard for most exchanges)
- **Sleep Duration**: 1 hour between checks (configurable)

### Timing Configuration
```yaml
# Timing settings
max_sleep_seconds: 3600           # Maximum sleep duration between checks (1 hour)
default_sleep_seconds: 3600       # Default sleep when no events scheduled (1 hour)
monitoring_window_seconds: 300    # Window around monitoring time to trigger check (5 minutes)
```

### State Persistence
- Saves strategy timing state to JSON files
- Tracks rebalance and monitoring schedules across restarts
- Maintains execution timing continuity
- Provides scheduling recovery capabilities

### Performance Optimizations
- **Concurrent Data Fetching**: Parallel processing for market analysis and position calculations
- **Intelligent Caching**: Smart caching system with TTL and automatic garbage collection
- **Memory Management**: Configurable garbage collection to prevent memory leaks
- **Thread-Safe Operations**: Thread-safe cache operations for concurrent access
- **Batch Processing**: Processes symbols in configurable batches for memory efficiency
- **Rate Limiting**: Adaptive rate limiting to prevent exchange restrictions

### Data Quality & Validation
- **NaN Data Cleaning**: Automatic detection and removal of NaN/invalid data rows
- **Unit Consistency**: Comprehensive data unit validation across all exchanges
- **Contract Specifications**: Proper handling of minimum tradeable quantities
- **Volume Conversion**: Accurate base→quote volume conversion with validation
- **Funding Rate Validation**: Cleaning of extreme or invalid funding rate entries
- **Error Recovery**: Robust error handling with graceful degradation

### Exchange Integration
- **Unified Interface**: Consistent API across Bybit, Binance, OKX, and Hyperliquid
- **Exchange-Specific Optimization**: Tailored parameters for each exchange's characteristics
- **Demo/Testnet Support**: Comprehensive testing environment support
- **Credential Management**: Flexible credential handling (config, environment, interactive)

## Configuration Examples

### Conservative Setup (Recommended for Beginners)
```yaml
total_capital_usd: 5000           # Start with smaller capital
peak_abs_funding_rate: 0.0005     # Conservative peak for sigmoid (0.05%)
max_abs_funding_rate: 0.005       # Conservative funding rate limit (0.5%)
max_position_capital_pct: 15      # Conservative max per position (15%)
min_positions_per_leg: 3          # Minimum diversification
min_daily_volume_usd: 2000000     # Higher volume requirement ($2M)
simulation_mode: true             # Paper trading first
use_testnet: true                 # Use testnet environment
```

### Aggressive Setup (For Experienced Users)
```yaml
total_capital_usd: 50000          # Larger capital deployment
peak_abs_funding_rate: 0.001      # Standard peak for sigmoid (0.1%)
max_abs_funding_rate: 0.01        # Higher funding rate tolerance (1%)
max_position_capital_pct: 25      # Standard max per position (25%)
min_positions_per_leg: 2          # Minimum diversification
min_daily_volume_usd: 1000000     # Standard volume requirement ($1M)
simulation_mode: false            # Live trading
use_testnet: false                # Live environment
```

### High-Frequency Setup (Maximum Opportunities)
```yaml
total_capital_usd: 100000         # Large capital for diversification
peak_abs_funding_rate: 0.0015     # Higher peak for more opportunities (0.15%)
max_abs_funding_rate: 0.015       # High tolerance for opportunities (1.5%)
max_position_capital_pct: 30      # Higher max per position (30%)
min_positions_per_leg: 2          # Minimum diversification
min_daily_volume_usd: 500000      # Lower volume requirement ($500K)
max_sleep_seconds: 1800           # More frequent checks (30 minutes)
```

## Best Practices & Tips

### Getting Started
1. **Start Small**: Begin with testnet and small capital amounts
2. **Monitor Closely**: Watch the first few rebalancing cycles carefully
3. **Understand Costs**: Ensure funding collection exceeds trading costs
4. **Test Thoroughly**: Run in simulation mode before live trading

### Optimization Tips
- **EV Thresholds**: Start with conservative peak_abs_funding_rate (0.0005), increase gradually as you gain confidence
- **Position Limits**: Begin with lower max_position_capital_pct (15-20%), increase as strategy proves profitable
- **Volume Filtering**: Higher volume requirements reduce slippage but limit opportunities
- **Funding Rate Limits**: Conservative limits reduce risk but may miss profitable opportunities
- **Sleep Intervals**: Longer intervals save resources, shorter intervals catch more opportunities

### Monitoring & Maintenance
- **Daily Review**: Check logs and performance metrics daily
- **Weekly Analysis**: Analyze funding collection vs. trading costs weekly
- **Monthly Optimization**: Adjust parameters based on market conditions monthly
- **Backup Strategy**: Always have a plan for manual intervention if needed

## Frequently Asked Questions

### Strategy Questions

**Q: Why use mean instead of median funding rates?**
A: Mean funding rates are more sensitive to recent changes and provide better signal quality for trend detection, while median can mask important shifts in funding dynamics.

**Q: How does the cost adjustment floor protection work?**
A: Cost adjustments cannot flip the sign of funding rates. If a positive funding rate minus cost would go negative, it's floored at zero. Similarly, negative rates plus cost are ceilinged at zero.

**Q: How does the EV-based approach differ from percentile-based selection?**
A: EV-based selection uses all coins with positive Expected Value (non-zero adjusted funding) rather than fixed percentiles. It applies sigmoid weighting to allocate capital based on funding rate strength, allowing for more dynamic position counts and better capital utilization.

**Q: What is the sigmoid weighting function?**
A: The function `x * exp(-(x/peak)²)` maps funding rates to position weights. It peaks at the configured `peak_abs_funding_rate` and approaches zero for extreme values, automatically controlling risk while maximizing opportunities.

**Q: Why equal dollar volatility instead of equal position counts?**
A: Equal dollar volatility ensures true market neutrality by balancing risk exposure between legs. This allows for different numbers of longs vs shorts based on market opportunities while maintaining overall portfolio balance.

### Technical Questions

**Q: How often does the strategy rebalance?**
A: Daily at 23:00 UTC (1 hour before funding), with monitoring checks at 23:00, 07:00, and 15:00 UTC.

**Q: What happens if the strategy crashes?**
A: The strategy saves state persistently and can resume from the last known state. Timing continuity is maintained across restarts.

**Q: How does the randomized execution work?**
A: Orders are split into 3-5 random batches with random timing (60-300s intervals) and random orderbook placement to reduce market impact.

**Q: "No eligible coins found" - what should I do?**
A: Check your filters - reduce `min_daily_volume_usd`, increase `max_abs_funding_rate`, or decrease `min_volatility_threshold`.

**Q: Orders keep getting rejected - why?**
A: Likely post-only rejections due to stale orderbook data. The strategy automatically retries with fresh orderbook data.

## Testing

The strategy includes comprehensive unit tests covering all major components with **54 total tests** across **6 specialized test modules**:

### Test Coverage
- **Sigmoid Function Tests**: Mathematical properties, edge cases, monotonicity
- **Position Selection Tests**: EV-based selection logic, filtering, ranking
- **Position Sizing Tests**: Capital allocation, volatility balancing, constraints
- **Volatility Calculation Tests**: Weighted volatility, dollar volatility balancing
- **Configuration Validation Tests**: Parameter validation, bounds checking
- **End-to-End Integration Tests**: Complete strategy pipeline testing

### Running Tests

```bash
# Run comprehensive test suite with detailed reporting
cd tests
python run_all_ev_tests.py

# Run individual test modules
python -m pytest test_ev_sigmoid_function.py -v
python -m pytest test_ev_position_selection.py -v
python -m pytest test_ev_position_sizing.py -v
python -m pytest test_ev_volatility_calculations.py -v
python -m pytest test_ev_config_validation.py -v
python -m pytest test_ev_end_to_end.py -v

# Generate test report
# Report will be saved as tests/EV_STRATEGY_TEST_REPORT.md
```

### Test Results Summary
- **Total Tests**: 54
- **Core Mathematical Functions**: ✅ Verified
- **Business Logic**: ✅ Thoroughly tested
- **Edge Cases**: ✅ Comprehensively covered
- **Integration**: ✅ End-to-end pipeline tested

### Mathematical Formula Verification
- **Sigmoid Weighting**: `x * exp(-(x/peak)²)` ✅ Verified
- **Weighted Volatility**: `0.3×vol60 + 0.5×vol30 + 0.2×vol10` ✅ Verified
- **Dollar Volatility Balancing**: Equal dollar vol between legs ✅ Verified
- **Volatility Targeting**: `target_vol/coin_vol` with leverage bounds ✅ Verified
