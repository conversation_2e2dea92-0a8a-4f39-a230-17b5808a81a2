"""
Cross-Sectional Momentum Strategy

This strategy implements momentum-based trading by:
1. Selecting universe from top 50 coins by market cap (excluding stablecoins)
2. Calculating 20-day z-score of daily close prices as momentum feature
3. Using sigmoid weighting for position allocation
4. Applying volatility targeting and 60-day rolling beta projection
"""

from .strategy import CrossSectionalMomentumStrategy

__all__ = ['CrossSectionalMomentumStrategy']
