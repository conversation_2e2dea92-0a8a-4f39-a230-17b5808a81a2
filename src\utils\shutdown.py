"""
Graceful shutdown handling for the trading strategy
"""

import asyncio
import logging
import signal
import sys
import time
from typing import Optional, Callable, List, Dict, Any
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


class ShutdownCoordinator:
    """
    Coordinates graceful shutdown of the trading strategy.
    
    Handles:
    - Signal registration (SIGINT, SIGTERM)
    - Resource cleanup coordination
    - Timeout management
    - State preservation
    - Order cancellation
    - Exchange connection cleanup
    """
    
    def __init__(self, shutdown_timeout: float = 60.0):
        self.shutdown_timeout = shutdown_timeout
        self.shutdown_requested = False
        self.shutdown_in_progress = False
        self.shutdown_start_time: Optional[float] = None
        
        # Cleanup callbacks
        self.cleanup_callbacks: List[Callable] = []
        self.async_cleanup_callbacks: List[Callable] = []
        
        # Strategy components
        self.strategy = None
        self.exchange = None
        self.executor = None
        self.state_manager = None
        
        # Running tasks to cancel
        self.running_tasks: List[asyncio.Task] = []
        
        # Shutdown event
        self.shutdown_event = asyncio.Event()
        
        logger.info(f"🛡️ Shutdown coordinator initialized (timeout: {shutdown_timeout}s)")
    
    def register_strategy_components(self, strategy=None, exchange=None, executor=None, state_manager=None):
        """Register strategy components for cleanup"""
        self.strategy = strategy
        self.exchange = exchange
        self.executor = executor
        self.state_manager = state_manager
        logger.debug("📋 Strategy components registered for shutdown handling")
    
    def add_cleanup_callback(self, callback: Callable):
        """Add a synchronous cleanup callback"""
        self.cleanup_callbacks.append(callback)
        logger.debug(f"📋 Added cleanup callback: {callback.__name__}")
    
    def add_async_cleanup_callback(self, callback: Callable):
        """Add an asynchronous cleanup callback"""
        self.async_cleanup_callbacks.append(callback)
        logger.debug(f"📋 Added async cleanup callback: {callback.__name__}")
    
    def register_task(self, task: asyncio.Task):
        """Register a running task for cancellation during shutdown"""
        self.running_tasks.append(task)
        logger.debug(f"📋 Registered task for shutdown handling: {task.get_name()}")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        try:
            # Handle SIGINT (Ctrl+C)
            signal.signal(signal.SIGINT, self._signal_handler)
            
            # Handle SIGTERM (termination request)
            if hasattr(signal, 'SIGTERM'):
                signal.signal(signal.SIGTERM, self._signal_handler)
            
            logger.info("🛡️ Signal handlers registered for graceful shutdown")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to setup signal handlers: {e}")
    
    def _signal_handler(self, signum: int, frame):
        """Handle shutdown signals"""
        signal_name = signal.Signals(signum).name if hasattr(signal, 'Signals') else str(signum)
        logger.info(f"🛑 Received {signal_name} signal - initiating graceful shutdown...")
        
        if not self.shutdown_requested:
            self.shutdown_requested = True
            self.shutdown_event.set()
        else:
            logger.warning("⚠️ Shutdown already in progress - forcing exit...")
            sys.exit(1)
    
    def request_shutdown(self, reason: str = "Manual request"):
        """Request graceful shutdown programmatically"""
        if not self.shutdown_requested:
            logger.info(f"🛑 Shutdown requested: {reason}")
            self.shutdown_requested = True
            self.shutdown_event.set()
    
    async def wait_for_shutdown(self):
        """Wait for shutdown signal"""
        await self.shutdown_event.wait()
    
    def is_shutdown_requested(self) -> bool:
        """Check if shutdown has been requested"""
        return self.shutdown_requested
    
    async def execute_graceful_shutdown(self) -> bool:
        """
        Execute graceful shutdown sequence.
        
        Returns:
            True if shutdown completed successfully, False if timeout
        """
        if self.shutdown_in_progress:
            logger.warning("⚠️ Shutdown already in progress")
            return False
        
        self.shutdown_in_progress = True
        self.shutdown_start_time = time.time()
        
        logger.info("🛑 Starting graceful shutdown sequence...")
        
        try:
            # Step 1: Stop strategy execution
            await self._stop_strategy()
            
            # Step 2: Cancel all running orders
            await self._cancel_all_orders()
            
            # Step 3: Cancel running tasks
            await self._cancel_running_tasks()
            
            # Step 4: Execute async cleanup callbacks
            await self._execute_async_cleanup_callbacks()
            
            # Step 5: Execute sync cleanup callbacks
            self._execute_cleanup_callbacks()
            
            # Step 6: Save final state
            await self._save_final_state()
            
            # Step 7: Close exchange connections
            await self._close_exchange_connections()
            
            elapsed = time.time() - self.shutdown_start_time
            logger.info(f"✅ Graceful shutdown completed successfully in {elapsed:.1f}s")
            return True
            
        except asyncio.TimeoutError:
            elapsed = time.time() - self.shutdown_start_time
            logger.error(f"❌ Graceful shutdown timed out after {elapsed:.1f}s")
            return False
            
        except Exception as e:
            elapsed = time.time() - self.shutdown_start_time
            logger.error(f"❌ Error during graceful shutdown after {elapsed:.1f}s: {e}")
            return False
    
    async def _stop_strategy(self):
        """Stop strategy execution"""
        if self.strategy:
            logger.info("🛑 Stopping strategy execution...")
            try:
                self.strategy.stop()
                logger.info("✅ Strategy stopped")
            except Exception as e:
                logger.error(f"❌ Error stopping strategy: {e}")
    
    async def _cancel_all_orders(self):
        """Cancel all open orders"""
        if self.executor:
            logger.info("🚫 Canceling all open orders...")
            try:
                await asyncio.wait_for(
                    self.executor._cancel_all_open_orders(),
                    timeout=30.0
                )
                logger.info("✅ All orders canceled")
            except asyncio.TimeoutError:
                logger.error("❌ Order cancellation timed out")
            except Exception as e:
                logger.error(f"❌ Error canceling orders: {e}")
    
    async def _cancel_running_tasks(self):
        """Cancel all running tasks"""
        if self.running_tasks:
            logger.info(f"🚫 Canceling {len(self.running_tasks)} running tasks...")
            
            for task in self.running_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete cancellation
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.running_tasks, return_exceptions=True),
                    timeout=10.0
                )
                logger.info("✅ All tasks canceled")
            except asyncio.TimeoutError:
                logger.error("❌ Task cancellation timed out")
    
    async def _execute_async_cleanup_callbacks(self):
        """Execute all async cleanup callbacks"""
        if self.async_cleanup_callbacks:
            logger.info(f"🧹 Executing {len(self.async_cleanup_callbacks)} async cleanup callbacks...")
            
            for callback in self.async_cleanup_callbacks:
                try:
                    await asyncio.wait_for(callback(), timeout=10.0)
                    logger.debug(f"✅ Async cleanup callback completed: {callback.__name__}")
                except Exception as e:
                    logger.error(f"❌ Async cleanup callback failed: {callback.__name__}: {e}")
    
    def _execute_cleanup_callbacks(self):
        """Execute all sync cleanup callbacks"""
        if self.cleanup_callbacks:
            logger.info(f"🧹 Executing {len(self.cleanup_callbacks)} cleanup callbacks...")
            
            for callback in self.cleanup_callbacks:
                try:
                    callback()
                    logger.debug(f"✅ Cleanup callback completed: {callback.__name__}")
                except Exception as e:
                    logger.error(f"❌ Cleanup callback failed: {callback.__name__}: {e}")
    
    async def _save_final_state(self):
        """Save final state"""
        if self.state_manager:
            logger.info("💾 Saving final state...")
            try:
                self.state_manager.save_state()
                logger.info("✅ Final state saved")
            except Exception as e:
                logger.error(f"❌ Error saving final state: {e}")
    
    async def _close_exchange_connections(self):
        """Close exchange connections"""
        if self.exchange:
            logger.info("🔌 Closing exchange connections...")
            try:
                if hasattr(self.exchange, 'close'):
                    await self.exchange.close()
                logger.info("✅ Exchange connections closed")
            except Exception as e:
                logger.error(f"❌ Error closing exchange connections: {e}")
    
    def get_shutdown_status(self) -> Dict[str, Any]:
        """Get current shutdown status"""
        elapsed = time.time() - self.shutdown_start_time if self.shutdown_start_time else 0
        
        return {
            'shutdown_requested': self.shutdown_requested,
            'shutdown_in_progress': self.shutdown_in_progress,
            'elapsed_time': elapsed,
            'timeout': self.shutdown_timeout,
            'remaining_time': max(0, self.shutdown_timeout - elapsed) if self.shutdown_in_progress else None
        }


# Global shutdown coordinator instance
shutdown_coordinator = ShutdownCoordinator()
