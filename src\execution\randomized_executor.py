#!/usr/bin/env python3

"""
Randomized Batch Execution System

This module implements a clean, robust execution system that:
1. Fetches current positions
2. Calculates deltas (target - current)
3. Executes deltas using randomized batching
4. Reconciles positions until target is achieved
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from exchanges.base import ExchangeInterface
from utils.contract_specs import contract_spec_manager
from utils.data_validation import DataUnitValidator
from utils.logging import get_logger
from utils.error_handler import ErrorRecoveryManager

logger = get_logger(__name__)


@dataclass
class BatchPlan:
    """Represents a single batch in the execution plan"""
    batch_number: int
    delta_percentage: float  # Percentage of total delta for this batch
    amount: float           # Actual amount to trade in this batch
    delay_seconds: int      # Delay before executing this batch
    orderbook_levels: int   # Number of orderbook levels to split across


@dataclass
class CoinExecutionPlan:
    """Represents the complete execution plan for a single coin"""
    symbol: str
    side: str              # 'buy' or 'sell'
    total_delta: float     # Total amount to trade
    batches: List[BatchPlan]


class RandomizedExecutor:
    """Clean randomized batch execution system"""
    
    def __init__(self, exchange: ExchangeInterface, config: Dict[str, Any]):
        self.exchange = exchange
        self.config = config

        # Enhanced error recovery manager
        self.error_manager = ErrorRecoveryManager(config.get('error_handling', {}))

        # Rate limiting configuration
        self.rate_limit_config = config.get('api_rate_limiting', {})
        self.execution_rate_config = config.get('execution_rate_limiting', {})

        # Rate limiting state
        self.last_order_time = 0
        self.order_count_per_second = 0
        self.order_count_per_minute = 0
        self.minute_start_time = time.time()

        # Emergency throttling state
        self.rate_limit_errors = 0
        self.throttle_until = 0

        # Global batch concurrency control
        max_concurrent_batches = self.execution_rate_config.get('max_concurrent_batches', 25)
        self.global_batch_semaphore = asyncio.Semaphore(max_concurrent_batches)

        # Memory management and cleanup
        self.order_history = {}  # Track order history for analysis
        self.last_cleanup = time.time()  # Track last cleanup time
        self.cleanup_interval = config.get('cleanup_interval_seconds', 3600)  # Cleanup every hour

    async def _check_rate_limits(self) -> None:
        """Check and enforce rate limits before making API calls"""
        current_time = time.time()

        # Check emergency throttling
        if current_time < self.throttle_until:
            wait_time = self.throttle_until - current_time
            logger.info(f"🛑 Emergency throttling active, waiting {wait_time:.1f}s...")
            await asyncio.sleep(wait_time)
            return

        # Reset minute counter if needed
        if current_time - self.minute_start_time >= 60:
            self.order_count_per_minute = 0
            self.minute_start_time = current_time

        # Check per-minute limit
        max_orders_per_minute = self.rate_limit_config.get('order_placement', {}).get('max_orders_per_minute', 100)
        if self.order_count_per_minute >= max_orders_per_minute:
            wait_time = 60 - (current_time - self.minute_start_time)
            if wait_time > 0:
                logger.info(f"⏱️ Per-minute rate limit reached, waiting {wait_time:.1f}s...")
                await asyncio.sleep(wait_time)
                self.order_count_per_minute = 0
                self.minute_start_time = time.time()

        # Check per-second limit
        max_orders_per_second = self.rate_limit_config.get('order_placement', {}).get('max_orders_per_second', 3)
        time_since_last = current_time - self.last_order_time
        min_interval = 1.0 / max_orders_per_second

        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            await asyncio.sleep(wait_time)

        # Apply batch delay
        batch_delay = self.rate_limit_config.get('order_placement', {}).get('batch_delay_ms', 200) / 1000.0
        if batch_delay > 0:
            await asyncio.sleep(batch_delay)

    def _handle_rate_limit_error(self) -> None:
        """Handle rate limit errors with adaptive backoff"""
        self.rate_limit_errors += 1

        emergency_config = self.execution_rate_config.get('emergency_throttling', {})
        if not emergency_config.get('enabled', True):
            return

        threshold = emergency_config.get('rate_limit_threshold', 3)
        if self.rate_limit_errors >= threshold:
            throttle_delay = emergency_config.get('throttle_delay_seconds', 60)
            max_throttle = emergency_config.get('max_throttle_time', 300)

            # Exponential backoff
            backoff_multiplier = self.rate_limit_config.get('adaptive_limiting', {}).get('backoff_multiplier', 2.0)
            actual_delay = min(throttle_delay * (backoff_multiplier ** (self.rate_limit_errors - threshold)), max_throttle)

            self.throttle_until = time.time() + actual_delay
            logger.warning(f"🚨 Emergency throttling activated for {actual_delay:.1f}s after {self.rate_limit_errors} rate limit errors")

    def _reset_rate_limit_errors(self) -> None:
        """Reset rate limit error counter on successful operations"""
        if self.rate_limit_errors > 0:
            self.rate_limit_errors = 0
            logger.info("✅ Rate limit error counter reset")
        
    async def execute_to_target_positions(self, target_positions: List[Dict]) -> bool:
        """
        Main execution method that achieves target positions through randomized batching
        
        Args:
            target_positions: List of target position dictionaries
            
        Returns:
            bool: True if all positions achieved within tolerance
        """
        logger.info("🎯 Starting randomized batch execution to target positions")

        # Log position-based tolerance information (consolidated with buffer system)
        tolerance_pct = self.config.get('delta_tolerance_percentage', 0.25)
        logger.info(f"💰 Using position-based delta tolerance: {tolerance_pct}% of individual position sizes")

        max_iterations = self.config.get('max_execution_iterations', 10)  # Configurable max iterations
        iteration = 0

        debug_mode = self.config.get('debug_mode', False)

        while iteration < max_iterations:
            iteration += 1
            if debug_mode:
                logger.info(f"🔄 Execution iteration {iteration}/{max_iterations}")

            # Step 1: Fetch current positions
            current_positions = await self._fetch_current_positions()

            # Step 2: Calculate deltas
            deltas = self._calculate_deltas(target_positions, current_positions)

            if not deltas:
                logger.info(f"✅ All positions are within position-based tolerance ({tolerance_pct}%) - execution complete")
                return True

            if debug_mode:
                total_delta_usd = sum(delta.get('delta_usd', 0) for delta in deltas)
                logger.info(f"📊 Found {len(deltas)} coins requiring position adjustments (total delta: ${total_delta_usd:.2f})")

            # Step 3: Create execution plans for each coin
            execution_plans = self._create_execution_plans(deltas)

            # Step 4: Execute all plans with retry logic
            # On final iterations, consider aggressive execution if enabled
            is_final_iteration = iteration >= max_iterations - 1
            aggressive_mode = self.config.get('aggressive_execution_mode', False) and is_final_iteration

            if aggressive_mode:
                logger.warning(f"⚠️ Final iteration {iteration} - aggressive execution mode enabled")

            success = await self._execute_all_plans(execution_plans)

            if not success:
                logger.warning(f"⚠️ Some orders failed in iteration {iteration}, will retry after reconciliation")
                # Continue to reconciliation - partial fills may have occurred
            
            # Step 5: Wait for orders to settle, then cancel and reconcile
            await self._reconcile_positions_after_execution()

            logger.info(f"✅ Iteration {iteration} complete, checking final positions...")
        
        # Final verification: Check if we actually achieved the positions
        logger.warning(f"⚠️ Reached maximum iterations ({max_iterations}), performing final position check...")

        final_positions = await self._fetch_current_positions()
        final_deltas = self._calculate_deltas(target_positions, final_positions)

        if not final_deltas:
            tolerance_pct = self.config.get('delta_tolerance_percentage', 0.25)
            logger.info(f"✅ Final check: All positions achieved within position-based tolerance ({tolerance_pct}%)")
            return True
        else:
            total_remaining_usd = sum(delta.get('delta_usd', 0) for delta in final_deltas)
            tolerance_pct = self.config.get('delta_tolerance_percentage', 0.25)
            logger.error(f"❌ Final check: {len(final_deltas)} positions still not within tolerance")
            logger.error(f"❌ Total remaining delta: ${total_remaining_usd:.2f} USD")
            logger.error(f"❌ Execution failed - positions are not within position-based tolerance ({tolerance_pct}%)")
            return False

    async def _reconcile_positions_after_execution(self) -> None:
        """
        Reconciliation process after batch execution:
        1. Wait 5 minutes for orders to settle
        2. Cancel all open orders
        3. Ready for next iteration to check positions
        """
        # Wait for orders to settle (configurable, default 5 minutes)
        settle_time = self.config.get('order_settle_time_seconds', 300)  # 5 minutes default
        logger.info(f"⏱️ Waiting {settle_time} seconds for orders to settle...")
        await asyncio.sleep(settle_time)

        # Cancel all open orders to clean slate for next iteration
        await self._cancel_all_open_orders()

        logger.info("✅ Position reconciliation complete - ready for next iteration")

        # Perform periodic cleanup
        await self._perform_periodic_cleanup()

    async def _fetch_current_positions(self) -> Dict[str, float]:
        """
        Fetch current positions from exchange
        
        Returns:
            Dict mapping symbol to signed position size (positive=long, negative=short)
        """
        try:
            logger.info("📡 Fetching current positions from exchange...")
            positions = await self.exchange.fetch_positions()
            
            current_positions = {}
            # Use a small native tolerance for position filtering (not USD-based here)
            # This is just to filter out dust positions, main tolerance is USD-based in delta calculation
            native_tolerance = 0.001

            for pos in positions:
                if pos.get('contracts', 0) != 0:
                    symbol = pos['symbol']
                    size = float(pos['contracts'])
                    side = pos['side']

                    # Convert to signed size (positive=long, negative=short)
                    signed_size = size if side == 'long' else -size

                    # Only include positions above small native tolerance threshold
                    # Main USD-based tolerance checking happens in _calculate_deltas
                    if abs(signed_size) > native_tolerance:
                        current_positions[symbol] = signed_size
            
            logger.info(f"📊 Found {len(current_positions)} active positions")
            return current_positions
            
        except Exception as e:
            logger.error(f"❌ Error fetching current positions: {e}")
            return {}
    
    def _calculate_deltas(self, target_positions: List[Dict], current_positions: Dict[str, float]) -> List[Dict]:
        """
        Calculate position deltas (target - current) using USD-based tolerance

        Args:
            target_positions: List of target position dictionaries
            current_positions: Dict of current signed position sizes

        Returns:
            List of delta dictionaries for coins requiring adjustments
        """
        deltas = []

        # Create a set of all symbols (target + current)
        all_symbols = set()
        target_dict = {}
        target_prices = {}  # Store prices for USD conversion

        for target_pos in target_positions:
            symbol = target_pos['symbol']
            size = target_pos['size_native']
            side = target_pos['side']
            size_usd = target_pos['size_usd']

            # Convert to signed size
            signed_size = size if side == 'long' else -size
            target_dict[symbol] = signed_size
            all_symbols.add(symbol)

            # Calculate price from USD and native size for tolerance conversion
            if size > 0:
                target_prices[symbol] = size_usd / size

        # Add symbols that have current positions but no target
        all_symbols.update(current_positions.keys())

        # Calculate deltas for all symbols
        for symbol in all_symbols:
            target_size = target_dict.get(symbol, 0)
            current_size = current_positions.get(symbol, 0)

            delta_size = target_size - current_size

            # Convert delta to USD value for tolerance checking
            if symbol in target_prices:
                price = target_prices[symbol]
                delta_usd = abs(delta_size) * price
                target_usd = abs(target_size) * price

                # Calculate tolerance as percentage of target position size
                tolerance_pct = self.config.get('delta_tolerance_percentage', 0.25) / 100  # Default 0.25%
                position_tolerance_usd = target_usd * tolerance_pct

                # Use position-based tolerance
                if delta_usd > position_tolerance_usd:
                    side = 'buy' if delta_size > 0 else 'sell'
                    amount = abs(delta_size)

                    deltas.append({
                        'symbol': symbol,
                        'side': side,
                        'amount': amount,
                        'delta_size': delta_size,
                        'current_size': current_size,
                        'target_size': target_size,
                        'delta_usd': delta_usd,
                        'price': price
                    })

                    logger.debug(f"📊 {symbol}: {current_size:.6f} → {target_size:.6f} "
                               f"(delta: {side} {amount:.6f}, ${delta_usd:.2f} > ${position_tolerance_usd:.2f})")
            else:
                # For positions being closed (no target), use a small native tolerance
                native_tolerance = 0.001  # Small native amount for cleanup
                if abs(delta_size) > native_tolerance:
                    side = 'buy' if delta_size > 0 else 'sell'
                    amount = abs(delta_size)

                    deltas.append({
                        'symbol': symbol,
                        'side': side,
                        'amount': amount,
                        'delta_size': delta_size,
                        'current_size': current_size,
                        'target_size': target_size,
                        'delta_usd': 0,  # Unknown price for closing positions
                        'price': None
                    })

                    logger.debug(f"📊 {symbol}: {current_size:.6f} → {target_size:.6f} "
                               f"(closing position: {side} {amount:.6f})")

        return deltas
    
    def _create_execution_plans(self, deltas: List[Dict]) -> List[CoinExecutionPlan]:
        """
        Create randomized execution plans for each coin
        
        Args:
            deltas: List of delta dictionaries
            
        Returns:
            List of CoinExecutionPlan objects
        """
        execution_plans = []
        
        for delta in deltas:
            plan = self._create_single_coin_plan(delta)
            execution_plans.append(plan)
            
            logger.info(f"📋 {delta['symbol']}: {len(plan.batches)} batches, "
                       f"{delta['side']} {delta['amount']:.6f} total")
        
        return execution_plans

    def _create_single_coin_plan(self, delta: Dict) -> CoinExecutionPlan:
        """
        Create execution plan for a single coin with randomized batching

        Args:
            delta: Delta dictionary for the coin

        Returns:
            CoinExecutionPlan with randomized batches
        """
        symbol = delta['symbol']
        side = delta['side']
        total_amount = delta['amount']

        # Step 1: Random number of batches
        min_batches = self.config.get('min_batch_size', 3)
        max_batches = self.config.get('max_batch_size', 7)
        num_batches = random.randint(min_batches, max_batches)

        # Step 2: Generate random percentages that sum to 100%
        percentages = self._generate_random_percentages(num_batches)

        # Step 3: Create batches
        batches = []
        cumulative_delay = 0

        for i in range(num_batches):
            # Calculate amount for this batch
            batch_amount = total_amount * (percentages[i] / 100.0)

            # Random delay for this batch
            min_interval = self.config.get('min_batch_interval_seconds', 30)
            max_interval = self.config.get('max_batch_interval_seconds', 300)
            delay = random.randint(min_interval, max_interval) if i > 0 else 0
            cumulative_delay += delay

            # Random number of orderbook levels
            min_levels = self.config.get('min_orderbook_levels', 3)
            max_levels = self.config.get('max_orderbook_levels', 8)
            orderbook_levels = random.randint(min_levels, max_levels)

            batch = BatchPlan(
                batch_number=i + 1,
                delta_percentage=percentages[i],
                amount=batch_amount,
                delay_seconds=delay,
                orderbook_levels=orderbook_levels
            )
            batches.append(batch)

            logger.debug(f"  Batch {i+1}: {percentages[i]:.1f}% ({batch_amount:.6f}), "
                        f"delay: {delay}s, levels: {orderbook_levels}")

        return CoinExecutionPlan(
            symbol=symbol,
            side=side,
            total_delta=total_amount,
            batches=batches
        )

    def _generate_random_percentages(self, num_batches: int) -> List[float]:
        """
        Generate random percentages that sum to 100% with constraints

        Args:
            num_batches: Number of batches to generate percentages for

        Returns:
            List of percentages that sum to 100%
        """
        min_pct = self.config.get('min_delta_percentage', 10)
        max_pct = self.config.get('max_delta_percentage', 40)

        # Generate random percentages within constraints
        percentages = []
        remaining = 100.0

        for i in range(num_batches - 1):
            # Calculate max possible for this batch (leave room for remaining batches)
            remaining_batches = num_batches - i
            max_for_this_batch = min(max_pct, remaining - (remaining_batches - 1) * min_pct)
            min_for_this_batch = max(min_pct, remaining - (remaining_batches - 1) * max_pct)

            # Ensure valid range
            if max_for_this_batch < min_for_this_batch:
                max_for_this_batch = min_for_this_batch

            # Generate random percentage in valid range
            pct = random.uniform(min_for_this_batch, max_for_this_batch)
            percentages.append(pct)
            remaining -= pct

        # Last batch gets remaining percentage
        percentages.append(remaining)

        # Shuffle to randomize order
        random.shuffle(percentages)

        return percentages

    async def _execute_all_plans(self, execution_plans: List[CoinExecutionPlan]) -> bool:
        """
        Execute all coin plans with rate limiting and phased execution

        Args:
            execution_plans: List of execution plans

        Returns:
            bool: True if all plans executed successfully
        """
        logger.info(f"🚀 Executing {len(execution_plans)} coin plans with rate limiting...")

        # Check if phased execution is enabled
        phases_config = self.execution_rate_config.get('execution_phases', {})
        if phases_config.get('enabled', True) and len(execution_plans) > 20:
            return await self._execute_plans_in_phases(execution_plans)
        else:
            return await self._execute_plans_concurrently(execution_plans)

    async def _execute_plans_in_phases(self, execution_plans: List[CoinExecutionPlan]) -> bool:
        """Execute plans in phases to manage rate limits"""
        phases_config = self.execution_rate_config.get('execution_phases', {})
        phase_size = phases_config.get('phase_size', 50)
        phase_delay = phases_config.get('phase_delay_seconds', 30)

        total_plans = len(execution_plans)
        num_phases = (total_plans + phase_size - 1) // phase_size

        logger.info(f"📊 Executing {total_plans} plans in {num_phases} phases of {phase_size} coins each")

        overall_success = True

        for phase_num in range(num_phases):
            start_idx = phase_num * phase_size
            end_idx = min(start_idx + phase_size, total_plans)
            phase_plans = execution_plans[start_idx:end_idx]

            logger.info(f"🔄 Phase {phase_num + 1}/{num_phases}: executing {len(phase_plans)} coins")

            # Execute this phase
            phase_success = await self._execute_plans_concurrently(phase_plans)
            if not phase_success:
                overall_success = False

            # Wait between phases (except for the last one)
            if phase_num < num_phases - 1:
                logger.info(f"⏱️ Waiting {phase_delay}s before next phase...")
                await asyncio.sleep(phase_delay)

        return overall_success

    async def _execute_plans_concurrently(self, execution_plans: List[CoinExecutionPlan]) -> bool:
        """Execute a batch of plans concurrently with rate limiting"""
        max_concurrent = self.execution_rate_config.get('max_concurrent_coins', 10)
        coin_start_delay = self.execution_rate_config.get('coin_start_delay_ms', 500) / 1000.0

        # Limit concurrent execution
        if len(execution_plans) > max_concurrent:
            logger.info(f"⚡ Limiting concurrent execution to {max_concurrent} coins")

        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(max_concurrent)

        async def execute_with_semaphore(plan: CoinExecutionPlan) -> bool:
            async with semaphore:
                return await self._execute_single_coin_plan(plan)

        # Create tasks with staggered start times
        tasks = []
        for i, plan in enumerate(execution_plans):
            # Add delay before starting each coin
            if i > 0 and coin_start_delay > 0:
                await asyncio.sleep(coin_start_delay)

            task = asyncio.create_task(execute_with_semaphore(plan))
            tasks.append(task)

        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Check results
        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Plan {i+1} failed with exception: {result}")
            elif result:
                success_count += 1
            else:
                logger.error(f"❌ Plan {i+1} failed")

        if self.config.get('debug_mode', False):
            logger.info(f"📊 Execution results: {success_count}/{len(execution_plans)} plans successful")
        return success_count == len(execution_plans)

    async def _execute_single_coin_plan(self, plan: CoinExecutionPlan) -> bool:
        """
        Execute a single coin's batched plan with asynchronous batch execution

        Args:
            plan: CoinExecutionPlan to execute

        Returns:
            bool: True if all batches executed successfully
        """
        debug_mode = self.config.get('debug_mode', False)
        if debug_mode:
            logger.info(f"🎯 Executing {plan.symbol}: {len(plan.batches)} batches")

        # Check if async batch execution is enabled (check both global and execution-specific flags)
        global_async_flag = self.config.get('async_batch_execution', True)
        execution_async_flag = self.execution_rate_config.get('async_batches_per_coin', True)
        async_batches = global_async_flag and execution_async_flag

        if async_batches:
            if debug_mode:
                logger.info(f"🚀 {plan.symbol}: Using async batch execution for {len(plan.batches)} batches")
            return await self._execute_batches_async(plan)
        else:
            if debug_mode:
                logger.info(f"🔄 {plan.symbol}: Using sequential batch execution for {len(plan.batches)} batches")
            return await self._execute_batches_sequential(plan)

    async def _execute_batches_async(self, plan: CoinExecutionPlan) -> bool:
        """
        Execute all batches for a coin asynchronously with proper rate limiting

        Args:
            plan: CoinExecutionPlan to execute

        Returns:
            bool: True if all batches executed successfully
        """
        start_time = time.time()

        # Create semaphore to limit concurrent batches per coin
        batch_semaphore_size = self.execution_rate_config.get('batch_semaphore_size', 8)
        batch_semaphore = asyncio.Semaphore(batch_semaphore_size)

        # Calculate expected sequential time for comparison
        total_delay = sum(batch.delay_seconds for batch in plan.batches)
        logger.info(f"📊 {plan.symbol}: Sequential execution would take ~{total_delay}s, async execution starting...")

        async def execute_batch_with_delay_and_semaphore(batch: BatchPlan) -> bool:
            """Execute a single batch with delay and dual semaphore control"""
            # Use both global and per-coin semaphores for proper rate limiting
            async with self.global_batch_semaphore:
                async with batch_semaphore:
                    # Wait for the random delay first
                    if batch.delay_seconds > 0:
                        logger.info(f"⏱️ {plan.symbol} batch {batch.batch_number}: waiting {batch.delay_seconds}s...")
                        await asyncio.sleep(batch.delay_seconds)

                    # Then execute the batch
                    success = await self._execute_single_batch(plan.symbol, plan.side, batch)

                    if success:
                        logger.info(f"✅ {plan.symbol} batch {batch.batch_number} completed")
                    else:
                        logger.error(f"❌ {plan.symbol} batch {batch.batch_number} failed")

                    return success

        # Create tasks for all batches
        batch_tasks = []
        batch_start_delay = self.execution_rate_config.get('batch_start_delay_ms', 50) / 1000.0

        for i, batch in enumerate(plan.batches):
            # Add staggered start delay to prevent all batches starting simultaneously
            if i > 0 and batch_start_delay > 0:
                await asyncio.sleep(batch_start_delay)

            task = asyncio.create_task(execute_batch_with_delay_and_semaphore(batch))
            batch_tasks.append(task)

        # Wait for all batches to complete
        try:
            results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Check results
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"❌ {plan.symbol} batch {i+1} failed with exception: {result}")
                elif result:
                    success_count += 1
                else:
                    logger.error(f"❌ {plan.symbol} batch {i+1} failed")

            success = success_count == len(plan.batches)
            execution_time = time.time() - start_time

            if success:
                logger.info(f"✅ {plan.symbol} all {len(plan.batches)} batches completed successfully in {execution_time:.1f}s")
                logger.info(f"⚡ {plan.symbol}: Async execution saved ~{total_delay - execution_time:.1f}s compared to sequential")
            else:
                logger.error(f"❌ {plan.symbol} only {success_count}/{len(plan.batches)} batches completed successfully in {execution_time:.1f}s")

            return success

        except Exception as e:
            logger.error(f"❌ {plan.symbol} async batch execution failed: {e}")
            return False

    async def _execute_batches_sequential(self, plan: CoinExecutionPlan) -> bool:
        """
        Execute batches sequentially (fallback method)

        Args:
            plan: CoinExecutionPlan to execute

        Returns:
            bool: True if all batches executed successfully
        """
        batch_start_delay = self.execution_rate_config.get('batch_start_delay_ms', 100) / 1000.0

        for i, batch in enumerate(plan.batches):
            # Add delay between batches (in addition to the random delay)
            if i > 0 and batch_start_delay > 0:
                await asyncio.sleep(batch_start_delay)

            # Wait for batch delay
            if batch.delay_seconds > 0:
                logger.info(f"⏱️ {plan.symbol} batch {batch.batch_number}: waiting {batch.delay_seconds}s...")
                await asyncio.sleep(batch.delay_seconds)

            # Execute this batch
            success = await self._execute_single_batch(plan.symbol, plan.side, batch)

            if not success:
                logger.error(f"❌ {plan.symbol} batch {batch.batch_number} failed")
                return False

            logger.info(f"✅ {plan.symbol} batch {batch.batch_number} completed")

        logger.info(f"✅ {plan.symbol} all batches completed")
        return True

    async def _execute_single_batch(self, symbol: str, side: str, batch: BatchPlan) -> bool:
        """
        Execute a single batch by splitting across multiple orderbook levels

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            batch: BatchPlan to execute

        Returns:
            bool: True if batch executed successfully
        """
        try:
            logger.info(f"📋 {symbol} batch {batch.batch_number}: {side} {batch.amount:.6f} "
                       f"across {batch.orderbook_levels} levels")

            # Fetch fresh orderbook once per batch (not per order level)
            # This is critical to prevent post-only rejections due to stale data
            try:
                orderbook = await self.exchange.fetch_order_book(symbol)
            except Exception as ob_error:
                logger.warning(f"⚠️ {symbol}: Failed to fetch orderbook for batch {batch.batch_number}: {ob_error}")
                return False

            # Split batch amount across orderbook levels
            level_amounts = self._split_amount_across_levels(batch.amount, batch.orderbook_levels)

            # Place orders at each level with rate limiting
            orders_placed = []
            order_placement_delay = self.execution_rate_config.get('order_placement_delay_ms', 50) / 1000.0

            for level_idx, amount in enumerate(level_amounts):
                if amount <= 0:
                    continue

                # Add delay between individual order placements
                if level_idx > 0 and order_placement_delay > 0:
                    await asyncio.sleep(order_placement_delay)

                # Get price for this orderbook level using the batch orderbook
                base_price = self._get_orderbook_level_price(orderbook, side, level_idx)
                if base_price is None:
                    logger.warning(f"⚠️ {symbol}: No price available at level {level_idx}")
                    continue

                # Apply configurable price offset
                price = self._apply_price_offset(base_price, side)

                # Round amount and price according to contract specs
                rounded_amount = contract_spec_manager.round_quantity(symbol, amount, round_up=True)
                rounded_price = contract_spec_manager.round_price(symbol, price, round_up=(side == 'sell'))

                # Validate order parameters
                if not DataUnitValidator.validate_order_parameters(symbol, side, rounded_amount, rounded_price):
                    logger.warning(f"⚠️ {symbol}: Invalid order parameters at level {level_idx}")
                    continue

                # Place post-only order with fresh orderbook data and retry logic
                order_id = await self._place_post_only_order_with_retry(symbol, side, rounded_amount, rounded_price, level_idx)

                if order_id:
                    orders_placed.append(order_id)
                    logger.debug(f"  Level {level_idx}: {side} {rounded_amount:.6f} @ {rounded_price:.6f}")

            if orders_placed:
                logger.info(f"✅ {symbol} batch {batch.batch_number}: {len(orders_placed)} orders placed")
                return True
            else:
                logger.error(f"❌ {symbol} batch {batch.batch_number}: No orders placed")
                return False

        except Exception as e:
            logger.error(f"❌ Error executing batch for {symbol}: {e}")
            return False

    def _split_amount_across_levels(self, total_amount: float, num_levels: int) -> List[float]:
        """
        Split total amount across orderbook levels with random distribution

        Args:
            total_amount: Total amount to split
            num_levels: Number of levels to split across

        Returns:
            List of amounts for each level
        """
        if num_levels == 1:
            return [total_amount]

        # Generate random weights
        weights = [random.random() for _ in range(num_levels)]
        total_weight = sum(weights)

        # Normalize weights and calculate amounts
        amounts = []
        remaining = total_amount

        for i in range(num_levels - 1):
            weight_ratio = weights[i] / total_weight
            amount = total_amount * weight_ratio
            amounts.append(amount)
            remaining -= amount

        # Last level gets remaining amount
        amounts.append(remaining)

        return amounts

    def _get_orderbook_level_price(self, orderbook: Dict, side: str, level_idx: int) -> Optional[float]:
        """
        Get price at specific orderbook level for POST-ONLY orders

        For post-only orders starting from best bid/ask:
        - Buy orders (longs): start from best bid and go deeper (level 0 = best bid, level 1 = next bid, etc.)
        - Sell orders (shorts): start from best ask and go deeper (level 0 = best ask, level 1 = next ask, etc.)

        Args:
            orderbook: Orderbook data
            side: 'buy' or 'sell'
            level_idx: Level index (0 = best price, 1 = next level, etc.)

        Returns:
            Price at the specified level, or None if not available
        """
        try:
            if side == 'buy':
                # For BUY orders: use BID prices starting from best bid
                bids = orderbook.get('bids', [])
                if level_idx < len(bids):
                    return bids[level_idx][0]
            else:  # sell
                # For SELL orders: use ASK prices starting from best ask
                asks = orderbook.get('asks', [])
                if level_idx < len(asks):
                    return asks[level_idx][0]

            return None

        except Exception as e:
            logger.error(f"❌ Error getting orderbook level price: {e}")
            return None

    def _apply_price_offset(self, price: float, side: str) -> float:
        """
        Apply configurable price offset to move away from best bid/ask

        Args:
            price: Base price (best bid for buys, best ask for sells)
            side: 'buy' or 'sell'

        Returns:
            Price with offset applied
        """
        offset_pct = self.config.get('order_price_offset_pct', 0.0)  # Default 0% (no offset)

        if offset_pct == 0:
            return price  # No offset, use exact bid/ask prices

        if side == 'buy':
            # For buy orders, move price down (more conservative, away from spread)
            return price * (1 - offset_pct)
        else:
            # For sell orders, move price up (more conservative, away from spread)
            return price * (1 + offset_pct)

    async def _place_post_only_order_with_retry(self, symbol: str, side: str, amount: float, price: float, level_idx: int) -> Optional[str]:
        """
        Place a post-only order with fresh orderbook retry logic

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Order amount
            price: Order price
            level_idx: Orderbook level index for retry logic

        Returns:
            Order ID if successful, None otherwise
        """
        max_retries = self.config.get('post_only_max_retries', 2)
        retry_delay_ms = self.config.get('post_only_retry_delay_ms', 100)

        for attempt in range(max_retries + 1):
            try:
                # For retries, fetch fresh orderbook and recalculate price
                if attempt > 0:
                    logger.debug(f"🔄 {symbol}: Retry {attempt}/{max_retries} for post-only order at level {level_idx}")

                    # Small delay before retry
                    await asyncio.sleep(retry_delay_ms / 1000.0)

                    # Fetch fresh orderbook for retry
                    try:
                        fresh_orderbook = await self.exchange.fetch_order_book(symbol)
                        fresh_price = self._get_orderbook_level_price(fresh_orderbook, side, level_idx)

                        if fresh_price is None:
                            logger.warning(f"⚠️ {symbol}: No price available at level {level_idx} on retry {attempt}")
                            continue

                        # Apply configurable price offset to fresh price
                        price = self._apply_price_offset(fresh_price, side)

                        # Re-round the adjusted price
                        price = contract_spec_manager.round_price(symbol, price, round_up=(side == 'sell'))

                    except Exception as fresh_ob_error:
                        logger.warning(f"⚠️ {symbol}: Failed to fetch fresh orderbook on retry {attempt}: {fresh_ob_error}")
                        continue

                # Place the order
                order_id = await self._place_post_only_order(symbol, side, amount, price)

                if order_id:
                    if attempt > 0:
                        logger.info(f"✅ {symbol}: Post-only order succeeded on retry {attempt}")
                    return order_id

            except Exception as e:
                error_msg = str(e).lower()

                # Check if this is a post-only rejection that we should retry
                if any(phrase in error_msg for phrase in ['post only', 'postonly', 'would take', 'cross', 'taker']):
                    if attempt < max_retries:
                        logger.debug(f"🔄 {symbol}: Post-only rejected on attempt {attempt + 1}, will retry with fresh orderbook")
                        continue
                    else:
                        logger.debug(f"📋 {symbol}: Post-only order failed after {max_retries} retries")
                        break
                else:
                    # Non-post-only error, don't retry
                    logger.debug(f"📋 {symbol}: Order failed with non-post-only error: {e}")
                    break

        return None

    async def _place_post_only_order(self, symbol: str, side: str, amount: float, price: float) -> Optional[str]:
        """
        Place a post-only order with enhanced error handling and rate limiting

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Order amount
            price: Order price

        Returns:
            Order ID if successful, None otherwise
        """
        async def _execute_order():
            # Apply rate limiting before placing order
            await self._check_rate_limits()

            order_params = {
                'postOnly': True,
                'timeInForce': 'GTX'  # Good Till Crossing (post-only)
            }

            order = await self.exchange.create_limit_order(symbol, side, amount, price, order_params)

            # Update rate limiting counters
            self.last_order_time = time.time()
            self.order_count_per_second += 1
            self.order_count_per_minute += 1

            # Reset error counter on success
            self._reset_rate_limit_errors()

            if order and order.get('id'):
                return order['id']
            else:
                logger.warning(f"⚠️ {symbol}: Order placed but no ID returned")
                return None

        try:
            # Use enhanced error recovery manager
            return await self.error_manager.execute_with_retry(
                _execute_order,
                exchange=self.exchange.name,
                symbol=symbol,
                operation="place_order"
            )
        except Exception as e:
            # Fallback to legacy rate limit handling
            error_msg = str(e).lower()
            if any(phrase in error_msg for phrase in ['rate limit', 'too many', 'exceeded', 'throttle']):
                logger.warning(f"⚠️ {symbol}: Rate limit hit after retries - {e}")
                self._handle_rate_limit_error()
            else:
                logger.warning(f"⚠️ {symbol}: Order placement failed after retries - {e}")
            return None

    async def _cancel_all_open_orders(self) -> None:
        """Cancel all open orders before position reconciliation"""
        try:
            logger.info("🚫 Canceling all open orders...")

            # Get all open orders - handle exchange-specific requirements
            all_open_orders = []

            try:
                # Try to fetch all open orders without symbol (works for most exchanges)
                all_open_orders = await self.exchange.fetch_open_orders()
            except Exception as e:
                # If that fails, some exchanges (like Bybit) require symbol parameter
                # In this case, we need to get open orders for each symbol individually
                logger.debug(f"Failed to fetch all open orders without symbol: {e}")

                # Get current positions to know which symbols might have open orders
                try:
                    current_positions = await self._fetch_current_positions()
                    symbols_with_positions = list(current_positions.keys())

                    if symbols_with_positions:
                        logger.info(f"🔍 Fetching open orders for {len(symbols_with_positions)} symbols with positions...")

                        for symbol in symbols_with_positions:
                            try:
                                symbol_orders = await self.exchange.fetch_open_orders(symbol)
                                all_open_orders.extend(symbol_orders)
                            except Exception as symbol_error:
                                logger.warning(f"⚠️ Failed to fetch open orders for {symbol}: {symbol_error}")
                    else:
                        logger.info("📋 No positions found, assuming no open orders")

                except Exception as pos_error:
                    logger.warning(f"⚠️ Failed to fetch positions for order cancellation: {pos_error}")

            if not all_open_orders:
                logger.info("📋 No open orders to cancel")
                return

            # Cancel all orders
            cancel_tasks = []
            for order in all_open_orders:
                task = asyncio.create_task(
                    self.exchange.cancel_order(order['id'], order['symbol'])
                )
                cancel_tasks.append(task)

            # Wait for all cancellations
            results = await asyncio.gather(*cancel_tasks, return_exceptions=True)

            success_count = sum(1 for r in results if not isinstance(r, Exception))
            logger.info(f"🚫 Canceled {success_count}/{len(all_open_orders)} open orders")

        except Exception as e:
            logger.error(f"❌ Error canceling open orders: {e}")

    async def _perform_periodic_cleanup(self) -> None:
        """
        Perform periodic cleanup of memory and tracking data to prevent memory leaks
        """
        current_time = time.time()

        # Only cleanup if enough time has passed
        if current_time - self.last_cleanup < self.cleanup_interval:
            return

        logger.info("🧹 Performing periodic cleanup...")

        # Clean up old order history (keep only last 7 days)
        cutoff_time = current_time - (7 * 24 * 3600)  # 7 days ago
        orders_removed = 0

        for symbol in list(self.order_history.keys()):
            if symbol in self.order_history:
                # Remove old orders
                old_orders = [order for order in self.order_history[symbol]
                             if order.get('timestamp', 0) < cutoff_time]
                orders_removed += len(old_orders)

                # Keep only recent orders
                self.order_history[symbol] = [order for order in self.order_history[symbol]
                                            if order.get('timestamp', 0) >= cutoff_time]

                # Remove empty symbol entries
                if not self.order_history[symbol]:
                    del self.order_history[symbol]

        # Force garbage collection
        import gc
        collected = gc.collect()

        # Update cleanup time
        self.last_cleanup = current_time

        if self.config.get('debug_mode', False):
            logger.debug(f"🧹 Cleanup complete: removed {orders_removed} old orders, "
                        f"collected {collected} objects")
        else:
            logger.info(f"🧹 Cleanup complete: removed {orders_removed} old orders")
