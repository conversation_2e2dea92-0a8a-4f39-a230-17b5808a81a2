"""
Abstract base class for exchange implementations
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any


class ExchangeInterface(ABC):
    """Abstract interface for exchange implementations"""

    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize exchange connection"""
        pass

    @abstractmethod
    async def fetch_markets(self) -> Dict[str, Any]:
        """Fetch available markets"""
        pass

    @abstractmethod
    async def fetch_tickers(self) -> Dict[str, Any]:
        """Fetch ticker data for all symbols"""
        pass

    @abstractmethod
    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """Fetch ticker data for a single symbol"""
        pass

    @abstractmethod
    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List[List]:
        """Fetch OHLCV data"""
        pass

    @abstractmethod
    async def fetch_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """Fetch current funding rate"""
        pass

    @abstractmethod
    async def fetch_funding_rate_history(self, symbol: str, limit: int) -> List[Dict]:
        """Fetch funding rate history"""
        pass

    @abstractmethod
    async def fetch_balance(self) -> Dict[str, Any]:
        """Fetch account balance"""
        pass

    @abstractmethod
    async def fetch_positions(self, symbols: Optional[List[str]] = None) -> List[Dict]:
        """Fetch positions"""
        pass

    @abstractmethod
    async def fetch_open_orders(self, symbol: str) -> List[Dict]:
        """Fetch open orders"""
        pass

    @abstractmethod
    async def fetch_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Fetch order status"""
        pass

    @abstractmethod
    async def fetch_order_book(self, symbol: str) -> Dict[str, Any]:
        """Fetch order book"""
        pass

    @abstractmethod
    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict[str, Any]:
        """Create limit order"""
        pass

    @abstractmethod
    async def create_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict[str, Any]:
        """Create market order"""
        pass

    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Cancel order"""
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """Exchange name"""
        pass
