# Cross-Sectional Momentum Strategy Configuration
# Strategy-specific parameters for momentum-based trading

# ============================================================================
# UNIVERSE SELECTION PARAMETERS
# ============================================================================

# Market cap and liquidity filters
top_coins_by_market_cap: 50           # Select top N coins by market cap
min_daily_volume_usd: 5000000         # Minimum daily volume filter ($5M for momentum)
exclude_stablecoins: true             # Exclude stablecoins from universe
stablecoin_keywords:                   # Keywords to identify stablecoins
  - "USD"
  - "USDT" 
  - "USDC"
  - "BUSD"
  - "DAI"
  - "TUSD"
  - "FDUSD"

# Data availability requirements
min_historical_data_days: 60          # Minimum days of historical data required
exclude_new_listings_days: 30         # Exclude coins listed within this many days

# ============================================================================
# FEATURE CALCULATION PARAMETERS
# ============================================================================

# Momentum calculation
momentum_lookback_days: 20             # Days for momentum calculation (20-day z-score)
momentum_feature_name: "momentum_zscore_20d"  # Name for the momentum feature

# Z-score calculation parameters
zscore_window_days: 60                 # Rolling window for z-score calculation
min_observations_for_zscore: 30       # Minimum observations required for z-score

# Price data preferences
price_field: "close"                   # Price field to use (close, open, high, low)
use_log_returns: true                  # Use log returns for momentum calculation

# Volatility calculation weights (same as StatArb for consistency)
volatility_weights:
  vol_60d: 0.3                         # 30% weight for 60-day volatility
  vol_30d: 0.5                         # 50% weight for 30-day volatility
  vol_10d: 0.2                         # 20% weight for 10-day volatility

# Beta calculation
beta_calculation_days: 60              # Number of days for rolling beta calculation
market_index_symbol: "BTCUSDT"         # Market index symbol for beta calculation

# ============================================================================
# POSITION SELECTION PARAMETERS
# ============================================================================

# Momentum-based selection
momentum_threshold: 0.5                # Minimum absolute z-score for position selection
peak_momentum_zscore: 2.0              # Peak z-score for sigmoid weighting
use_cross_sectional_ranking: true     # Use cross-sectional ranking vs absolute thresholds

# Position allocation
long_momentum_threshold: 0.5           # Minimum z-score for long positions
short_momentum_threshold: -0.5         # Maximum z-score for short positions (negative)
min_positions_per_leg: 3               # Minimum positions per leg for diversification

# Position ranking criteria
# 1. Primary: Momentum weight (sigmoid-weighted z-score)
# 2. Secondary: Absolute momentum z-score
# 3. Tertiary: Volume (tie-breaker)

# ============================================================================
# POSITION SIZING PARAMETERS
# ============================================================================

# Capital allocation
max_position_capital_pct: 20           # Maximum capital per position (20% for momentum)
target_volatility: 0.25                # Target portfolio volatility (25%)

# Risk management
enable_beta_projection: true           # Enable beta-neutral portfolio construction
beta_neutrality_tolerance: 0.05        # Maximum allowed portfolio beta deviation (±5%)
beta_optimization_max_weight_change: 0.20  # Maximum weight change during beta optimization

# Position constraints
min_leverage: 0.1                      # Minimum position leverage
max_leverage: 3.0                      # Maximum position leverage (lower for momentum)

# ============================================================================
# STRATEGY-SPECIFIC RISK CONTROLS
# ============================================================================

# Momentum-specific controls
max_momentum_exposure: 0.8             # Maximum total momentum exposure (80% of capital)
momentum_decay_factor: 0.95            # Daily decay factor for momentum signals
enable_momentum_reversal_protection: true  # Protect against momentum reversals

# Buffer zones for position management
buffer_zone_tolerance_percentage: 7.5  # Buffer zone around target position (7.5% for momentum)

# Position closure thresholds
min_close_threshold_usd: 25            # Minimum USD value to trigger position closure
momentum_exit_threshold: 0.1           # Exit when momentum z-score falls below this

# ============================================================================
# EXECUTION PREFERENCES
# ============================================================================

# Order execution preferences
preferred_execution_style: "balanced"  # conservative, balanced, aggressive
max_execution_time_minutes: 45         # Maximum time to spend executing positions

# Position update frequency
position_update_frequency: "daily"     # daily, twice_daily, hourly
enable_intraday_rebalancing: false     # Enable intraday momentum updates

# ============================================================================
# PERFORMANCE AND MONITORING
# ============================================================================

# Strategy-specific monitoring
enable_momentum_alerts: true           # Alert on extreme momentum changes
momentum_alert_threshold: 3.0          # Alert threshold for momentum z-score changes
track_momentum_decay: true             # Track momentum signal decay over time

# Performance tracking
track_individual_position_pnl: true    # Track P&L for each position
enable_strategy_attribution: true      # Enable detailed strategy attribution
track_momentum_performance: true       # Track momentum-specific performance metrics

# Logging preferences
log_position_selection_details: true   # Log detailed position selection reasoning
log_momentum_calculations: true        # Log momentum calculation details
log_zscore_distributions: false        # Log z-score distribution statistics (verbose)

# ============================================================================
# FALLBACK AND DEFAULT VALUES
# ============================================================================

# Default values for missing data
default_volatility: 0.30               # Default volatility fallback (30% for momentum)
default_beta: 1.0                      # Default beta fallback (1.0 = market beta)
default_momentum_zscore: 0.0           # Default momentum z-score fallback

# Error handling
skip_problematic_symbols: true         # Skip symbols that fail validation
max_symbol_failures_per_execution: 8   # Maximum symbol failures before stopping
handle_momentum_outliers: true         # Handle extreme momentum outliers

# Data validation
validate_price_data: true              # Validate price data quality
validate_volume_data: true             # Validate volume data quality
validate_market_cap_data: true         # Validate market cap data quality
momentum_outlier_threshold: 5.0        # Z-score threshold for momentum outliers

# ============================================================================
# RESEARCH AND BACKTESTING PARAMETERS
# ============================================================================

# Research features (for future enhancement)
enable_momentum_regime_detection: false  # Detect momentum vs mean-reversion regimes
momentum_regime_lookback: 252           # Days for regime detection
enable_sector_momentum: false           # Enable sector-based momentum analysis

# Backtesting parameters
enable_transaction_cost_modeling: true  # Model transaction costs in backtesting
estimated_transaction_cost_bps: 10      # Estimated transaction cost in basis points
