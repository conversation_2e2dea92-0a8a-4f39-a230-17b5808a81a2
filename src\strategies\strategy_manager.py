"""
Strategy manager for coordinating multiple trading strategies

This module manages the execution of multiple strategies in parallel,
handles their lifecycle, and coordinates their results.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Type
from datetime import datetime

from .base import BaseStrategy, StrategyResult

logger = logging.getLogger(__name__)


class StrategyManager:
    """
    Manages multiple trading strategies and coordinates their execution
    
    The StrategyManager is responsible for:
    - Registering and managing strategy instances
    - Executing strategies in parallel
    - Handling strategy failures gracefully
    - Collecting and aggregating strategy results
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the strategy manager
        
        Args:
            config: Main configuration containing strategy settings
        """
        self.config = config
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_weights: Dict[str, float] = {}
        self.logger = logging.getLogger(__name__)
        
        # Extract strategy configuration
        self.strategies_config = config.get('strategies', {})
        self.max_concurrent_strategies = config.get('max_concurrent_strategies', 4)
        self.strategy_timeout_seconds = config.get('strategy_timeout_seconds', 300)  # 5 minutes default
        
        self.logger.info(f"🏗️ Strategy manager initialized with {len(self.strategies_config)} configured strategies")
    
    def register_strategy(self, strategy: BaseStrategy, weight: float = 1.0) -> None:
        """
        Register a strategy with the manager
        
        Args:
            strategy: Strategy instance to register
            weight: Portfolio weight for this strategy (default: 1.0)
        """
        if not isinstance(strategy, BaseStrategy):
            raise TypeError(f"Strategy must inherit from BaseStrategy, got {type(strategy)}")
        
        if weight <= 0:
            raise ValueError(f"Strategy weight must be positive, got {weight}")
        
        strategy_name = strategy.strategy_name
        
        if strategy_name in self.strategies:
            self.logger.warning(f"⚠️ Overwriting existing strategy: {strategy_name}")
        
        self.strategies[strategy_name] = strategy
        self.strategy_weights[strategy_name] = weight
        
        self.logger.info(f"✅ Registered strategy: {strategy_name} (weight: {weight})")
    
    def unregister_strategy(self, strategy_name: str) -> bool:
        """
        Unregister a strategy from the manager
        
        Args:
            strategy_name: Name of strategy to unregister
            
        Returns:
            True if strategy was found and removed, False otherwise
        """
        if strategy_name in self.strategies:
            del self.strategies[strategy_name]
            del self.strategy_weights[strategy_name]
            self.logger.info(f"✅ Unregistered strategy: {strategy_name}")
            return True
        else:
            self.logger.warning(f"⚠️ Strategy not found for unregistration: {strategy_name}")
            return False
    
    def get_registered_strategies(self) -> List[str]:
        """Get list of registered strategy names"""
        return list(self.strategies.keys())
    
    def get_strategy_weights(self) -> Dict[str, float]:
        """Get normalized strategy weights"""
        if not self.strategy_weights:
            return {}
        
        total_weight = sum(self.strategy_weights.values())
        return {name: weight / total_weight for name, weight in self.strategy_weights.items()}
    
    async def execute_all_strategies(self) -> Dict[str, StrategyResult]:
        """
        Execute all registered strategies in parallel
        
        Returns:
            Dictionary mapping strategy names to their results
        """
        if not self.strategies:
            self.logger.warning("⚠️ No strategies registered for execution")
            return {}
        
        self.logger.info(f"🚀 Executing {len(self.strategies)} strategies in parallel...")
        
        # Create execution tasks with timeout
        tasks = {}
        for strategy_name, strategy in self.strategies.items():
            task = asyncio.create_task(
                self._execute_strategy_with_timeout(strategy),
                name=f"strategy_{strategy_name}"
            )
            tasks[strategy_name] = task
        
        # Wait for all strategies to complete
        results = {}
        completed_count = 0
        failed_count = 0
        
        try:
            # Use asyncio.gather with return_exceptions=True to handle individual failures
            task_results = await asyncio.gather(*tasks.values(), return_exceptions=True)
            
            # Process results
            for (strategy_name, task), result in zip(tasks.items(), task_results):
                if isinstance(result, Exception):
                    self.logger.error(f"❌ Strategy {strategy_name} failed with exception: {result}")
                    # Create a failed result
                    results[strategy_name] = StrategyResult(
                        strategy_name=strategy_name,
                        target_positions=[],
                        total_capital_allocated=0.0,
                        execution_time_seconds=0.0,
                        universe_size=0,
                        candidates_count=0,
                        success=False,
                        error_message=str(result)
                    )
                    failed_count += 1
                else:
                    results[strategy_name] = result
                    if result.success:
                        completed_count += 1
                    else:
                        failed_count += 1
        
        except Exception as e:
            self.logger.error(f"❌ Critical error during strategy execution: {e}")
            # Cancel remaining tasks
            for task in tasks.values():
                if not task.done():
                    task.cancel()
            raise
        
        # Log execution summary
        total_strategies = len(self.strategies)
        self.logger.info(f"📊 Strategy execution summary:")
        self.logger.info(f"   ✅ Completed: {completed_count}/{total_strategies}")
        self.logger.info(f"   ❌ Failed: {failed_count}/{total_strategies}")
        
        # Log individual strategy results
        for strategy_name, result in results.items():
            if result.success:
                self.logger.info(f"   {strategy_name}: {result.position_count} positions, "
                               f"${result.total_capital_allocated:,.0f}, "
                               f"{result.execution_time_seconds:.2f}s")
            else:
                self.logger.error(f"   {strategy_name}: FAILED - {result.error_message}")
        
        return results
    
    async def _execute_strategy_with_timeout(self, strategy: BaseStrategy) -> StrategyResult:
        """
        Execute a single strategy with timeout protection
        
        Args:
            strategy: Strategy to execute
            
        Returns:
            StrategyResult from the strategy execution
        """
        try:
            # Execute strategy with timeout
            result = await asyncio.wait_for(
                strategy.execute(),
                timeout=self.strategy_timeout_seconds
            )
            
            return result
            
        except asyncio.TimeoutError:
            error_msg = f"Strategy execution timed out after {self.strategy_timeout_seconds} seconds"
            self.logger.error(f"❌ {strategy.strategy_name}: {error_msg}")
            
            return StrategyResult(
                strategy_name=strategy.strategy_name,
                target_positions=[],
                total_capital_allocated=0.0,
                execution_time_seconds=self.strategy_timeout_seconds,
                universe_size=0,
                candidates_count=0,
                success=False,
                error_message=error_msg
            )
        
        except Exception as e:
            error_msg = f"Strategy execution failed: {e}"
            self.logger.error(f"❌ {strategy.strategy_name}: {error_msg}")
            
            return StrategyResult(
                strategy_name=strategy.strategy_name,
                target_positions=[],
                total_capital_allocated=0.0,
                execution_time_seconds=0.0,
                universe_size=0,
                candidates_count=0,
                success=False,
                error_message=error_msg
            )
    
    def validate_strategies(self) -> bool:
        """
        Validate all registered strategies
        
        Returns:
            True if all strategies are valid, False otherwise
        """
        if not self.strategies:
            self.logger.error("❌ No strategies registered")
            return False
        
        all_valid = True
        
        for strategy_name, strategy in self.strategies.items():
            try:
                # Basic validation
                if not hasattr(strategy, 'execute'):
                    self.logger.error(f"❌ Strategy {strategy_name} missing execute method")
                    all_valid = False
                    continue
                
                # Check strategy info
                info = strategy.get_strategy_info()
                if not info.get('name'):
                    self.logger.error(f"❌ Strategy {strategy_name} has invalid info")
                    all_valid = False
                    continue
                
                self.logger.debug(f"✅ Strategy {strategy_name} validation passed")
                
            except Exception as e:
                self.logger.error(f"❌ Strategy {strategy_name} validation failed: {e}")
                all_valid = False
        
        if all_valid:
            self.logger.info(f"✅ All {len(self.strategies)} strategies validated successfully")
        else:
            self.logger.error(f"❌ Strategy validation failed")
        
        return all_valid
    
    def get_execution_summary(self, results: Dict[str, StrategyResult]) -> Dict[str, Any]:
        """
        Generate execution summary from strategy results
        
        Args:
            results: Dictionary of strategy results
            
        Returns:
            Summary statistics and metrics
        """
        if not results:
            return {'total_strategies': 0, 'successful': 0, 'failed': 0}
        
        successful = sum(1 for r in results.values() if r.success)
        failed = len(results) - successful
        total_positions = sum(r.position_count for r in results.values() if r.success)
        total_capital = sum(r.total_capital_allocated for r in results.values() if r.success)
        avg_execution_time = sum(r.execution_time_seconds for r in results.values()) / len(results)
        
        return {
            'total_strategies': len(results),
            'successful': successful,
            'failed': failed,
            'total_positions': total_positions,
            'total_capital_allocated': total_capital,
            'average_execution_time_seconds': avg_execution_time,
            'strategy_weights': self.get_strategy_weights()
        }
