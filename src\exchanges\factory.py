"""
Exchange factory for creating exchange instances
"""

from exchanges.base import ExchangeInterface
from exchanges.bybit import BybitExchange
from exchanges.binance import BinanceExchange
from exchanges.okx import OKXExchange
from exchanges.hyperliquid import HyperliquidExchange


class ExchangeFactory:
    """Factory class to create exchange instances"""

    @staticmethod
    def create_exchange(exchange_name: str) -> ExchangeInterface:
        """Create exchange instance based on name"""
        exchange_name = exchange_name.lower()

        if exchange_name == 'bybit':
            return BybitExchange()
        elif exchange_name == 'binance':
            return BinanceExchange()
        elif exchange_name == 'okx':
            return OKXExchange()
        elif exchange_name == 'hyperliquid':
            return HyperliquidExchange()
        else:
            raise ValueError(f"Unsupported exchange: {exchange_name}. Supported: {', '.join(ExchangeFactory.get_supported_exchanges())}")

    @staticmethod
    def get_supported_exchanges() -> list:
        """Get list of supported exchanges"""
        return ['bybit', 'binance', 'okx', 'hyperliquid']
