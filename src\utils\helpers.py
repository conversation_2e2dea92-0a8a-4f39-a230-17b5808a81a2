"""
Helper utilities and common functions
"""

import asyncio
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Optional


def format_currency(amount: float, currency: str = "USD", decimals: int = 2) -> str:
    """Format currency amount with proper formatting"""
    if currency.upper() == "USD":
        return f"${amount:,.{decimals}f}"
    else:
        return f"{amount:,.{decimals}f} {currency}"


def format_percentage(value: float, decimals: int = 2) -> str:
    """Format percentage with proper formatting"""
    return f"{value * 100:.{decimals}f}%"


def format_duration(seconds: float) -> str:
    """Format duration in human-readable format"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def calculate_sleep_time(target_time: datetime, buffer_seconds: int = 5) -> float:
    """Calculate sleep time until target time with buffer"""
    now = datetime.now(timezone.utc)
    if target_time.tzinfo is None:
        target_time = target_time.replace(tzinfo=timezone.utc)
    
    time_diff = (target_time - now).total_seconds()
    
    # Add buffer to wake up slightly before target
    sleep_time = max(0, time_diff - buffer_seconds)
    
    return sleep_time


def get_next_funding_times() -> list:
    """Get next funding times (UTC 00:00, 08:00, 16:00)"""
    now = datetime.now(timezone.utc)
    funding_hours = [0, 8, 16]  # 00:00, 08:00, 16:00 UTC
    
    next_times = []
    for hour in funding_hours:
        # Calculate next occurrence of this hour
        target = now.replace(hour=hour, minute=0, second=0, microsecond=0)
        
        # If time has passed today, move to tomorrow
        if target <= now:
            target += timedelta(days=1)
        
        next_times.append(target)
    
    return sorted(next_times)


def get_next_monitoring_time() -> datetime:
    """Get next monitoring time (1 hour before funding: 23:00, 07:00, 15:00)"""
    now = datetime.now(timezone.utc)
    monitoring_hours = [23, 7, 15]  # 23:00, 07:00, 15:00 UTC (1h before funding)

    next_times = []
    for hour in monitoring_hours:
        # Calculate next occurrence of this hour
        target = now.replace(hour=hour, minute=0, second=0, microsecond=0)

        # If time has passed today, move to tomorrow
        if target <= now:
            target += timedelta(days=1)

        next_times.append(target)

    return min(next_times)


def get_next_rebalance_time() -> datetime:
    """Get next rebalance time (always at 23:00 UTC)"""
    now = datetime.now(timezone.utc)

    # Calculate next 23:00
    target = now.replace(hour=23, minute=0, second=0, microsecond=0)

    # If 23:00 has passed today, move to tomorrow
    if target <= now:
        target += timedelta(days=1)

    return target


async def safe_gather(*coroutines, return_exceptions: bool = True):
    """Safely gather coroutines with exception handling"""
    try:
        results = await asyncio.gather(*coroutines, return_exceptions=return_exceptions)
        return results
    except Exception as e:
        # If gather itself fails, return list of exceptions
        return [e] * len(coroutines)


def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """Truncate string to maximum length"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def safe_float(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """Safely convert value to int"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def chunks(lst: list, chunk_size: int):
    """Yield successive chunks from list"""
    for i in range(0, len(lst), chunk_size):
        yield lst[i:i + chunk_size]


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator for retrying functions on exception"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        raise last_exception
        
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        raise last_exception
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, calls_per_second: float = 10.0):
        self.calls_per_second = calls_per_second
        self.min_interval = 1.0 / calls_per_second
        self.last_call = 0.0
    
    async def wait(self):
        """Wait if necessary to respect rate limit"""
        now = time.time()
        time_since_last = now - self.last_call
        
        if time_since_last < self.min_interval:
            sleep_time = self.min_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_call = time.time()


def validate_symbol(symbol: str) -> bool:
    """Validate if symbol format is correct"""
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Basic validation for crypto symbols
    if ':' in symbol and symbol.endswith(':USDT'):
        return True
    
    return False


def normalize_symbol(symbol: str, exchange: str = 'bybit') -> str:
    """Normalize symbol format for different exchanges"""
    if exchange.lower() == 'bybit':
        # Bybit uses format like 'BTC/USDT:USDT'
        if '/' not in symbol and ':' not in symbol:
            return f"{symbol}/USDT:USDT"
    elif exchange.lower() == 'binance':
        # Binance uses format like 'BTCUSDT'
        if '/' in symbol:
            base, quote = symbol.split('/')[0], symbol.split('/')[1].split(':')[0]
            return f"{base}{quote}"
    
    return symbol
