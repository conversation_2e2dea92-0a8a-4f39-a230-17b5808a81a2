# Beta Projection Implementation

## Overview

Beta projection is an advanced portfolio construction technique that ensures market neutrality by optimizing position weights to achieve a portfolio beta of zero. This implementation extends the existing volatility targeting system with sophisticated beta-neutral optimization.

## Key Concepts

### What is Beta?
Beta measures the sensitivity of an asset's returns to market returns:
- **Beta = 1.0**: Asset moves with the market
- **Beta > 1.0**: Asset is more volatile than the market  
- **Beta < 1.0**: Asset is less volatile than the market
- **Beta = 0.0**: No correlation with the market

### Portfolio Beta
Portfolio beta is the weighted average of individual asset betas:
```
Portfolio Beta = Σ(weight_i × beta_i)
```

### Beta Neutrality
A beta-neutral portfolio has a portfolio beta of zero, meaning it should be theoretically immune to broad market movements while still capturing alpha from individual asset selection.

## Implementation Architecture

### 1. Beta Calculator (`src/data/beta_calculator.py`)
- Calculates 60-day rolling beta for each asset against BTC (market proxy)
- Uses covariance/variance formula: `Beta = Cov(asset, market) / Var(market)`
- Implements robust error handling and caching
- Handles edge cases (zero variance, missing data, etc.)

### 2. Beta Optimizer (`src/execution/beta_optimizer.py`)
- Implements constrained optimization using scipy.optimize
- Minimizes weight changes while achieving beta neutrality
- Constraints:
  - Portfolio beta = 0 (beta neutrality)
  - Sum of weights = 1 (normalization)
  - Weight changes ≤ max_change (stability)

### 3. Integration Points
- **Data Analyzer**: Enriches coins with beta data alongside volatility
- **Position Manager**: Applies beta projection after volatility targeting
- **Configuration**: Centralized beta projection settings

## Mathematical Framework

### Optimization Problem
```
Minimize: ||w_new - w_original||²
Subject to:
  1. Σ(w_i × beta_i) = 0     (beta neutrality)
  2. Σ(w_i) = 1              (weight normalization)  
  3. |w_new_i - w_original_i| ≤ max_change  (weight change limits)
```

### Solution Method
- Uses Sequential Least Squares Programming (SLSQP)
- Handles numerical stability through proper bounds and constraints
- Validates results against all constraints before applying

## Configuration

### Basic Settings (config.yaml)
```yaml
# Beta Projection Settings
enable_beta_projection: true              # Enable/disable beta projection
market_index_symbol: "BTCUSDT"            # Market index for beta calculation
beta_neutrality_tolerance: 0.05           # Max allowed portfolio beta (±5%)
beta_optimization_max_weight_change: 0.20 # Max weight change during optimization (20%)

# Beta Calculation Settings
beta_calculation_days: 60                 # Rolling window for beta calculation
beta_cache_ttl: 3600                     # Cache TTL for beta values (1 hour)
default_beta: 1.0                        # Fallback beta value
```

## Workflow Integration

### 1. Data Enrichment Phase
```python
# In DataAnalyzer.enrich_coins_with_volatility()
if self.beta_calculator:
    betas = await asyncio.gather(*[
        self.beta_calculator.calculate_beta(coin['symbol']) 
        for coin in coins
    ])
    # Add beta data to coin dictionaries
```

### 2. Position Sizing Phase
```python
# In PositionManager.calculate_position_sizes_ev_based()
# After volatility targeting and leg balancing:
if self.beta_optimizer and adjusted_positions:
    adjusted_positions = await self._apply_beta_projection(adjusted_positions)
```

### 3. Beta Projection Phase
```python
# Extract betas and current weights
betas = [pos['beta'] for pos in positions]
weights = [pos['weight'] for pos in positions]

# Optimize for beta neutrality
optimized_positions = self.beta_optimizer.optimize_weights_for_beta_neutrality(
    positions, betas)

# Update position sizes based on optimized weights
# Recalculate contract-compliant sizes
```

## Key Features

### 1. Interacting Constraints
- **Not simple scaling**: Beta projection handles the interaction between volatility targeting and beta neutrality
- **Portfolio-level constraint**: Beta neutrality is a portfolio-wide requirement, not per-asset
- **Minimal disruption**: Optimization minimizes changes to volatility-targeted weights

### 2. Robust Implementation
- **Numerical stability**: Handles edge cases like zero variance, extreme betas
- **Validation**: Comprehensive result validation before applying changes
- **Fallback handling**: Graceful degradation if optimization fails
- **Contract compliance**: Maintains exchange-specific position sizing requirements

### 3. Performance Optimization
- **Caching**: Beta values cached to reduce computation
- **Concurrent calculation**: Parallel beta calculation for multiple assets
- **Efficient optimization**: Uses proven SLSQP algorithm for fast convergence

## Usage Examples

### Enable Beta Projection
```yaml
# config.yaml
enable_beta_projection: true
market_index_symbol: "BTCUSDT"
beta_neutrality_tolerance: 0.05
```

### Monitor Beta Projection
```
📊 Portfolio beta: 0.1234 → 0.0012 (after optimization)
🎯 ETH: weight 0.2500 → 0.2350 (Δ-0.0150, β=1.25)
🎯 ADA: weight 0.1500 → 0.1650 (Δ+0.0150, β=0.75)
✅ Beta projection completed - final portfolio beta: 0.0012
```

## Testing

### Unit Tests
- Beta calculation accuracy
- Portfolio beta computation
- Optimization constraint satisfaction
- Edge case handling

### Integration Tests
- End-to-end beta projection workflow
- Performance under various market conditions
- Interaction with existing volatility targeting

### Run Tests
```bash
python -m pytest tests/test_beta_projection.py -v
```

## Performance Considerations

### Computational Complexity
- **Beta calculation**: O(n) per asset for 60-day rolling window
- **Portfolio optimization**: O(n²) for n assets in optimization
- **Overall impact**: Minimal additional latency due to caching and parallel processing

### Memory Usage
- **Beta cache**: ~1KB per asset for 60-day data
- **Optimization**: Temporary arrays for constraint matrices
- **Total overhead**: <1MB for typical portfolio sizes

## Troubleshooting

### Common Issues

1. **Optimization Fails**
   - Check beta data availability
   - Verify weight change limits aren't too restrictive
   - Ensure sufficient position diversity

2. **High Portfolio Beta**
   - Increase `beta_neutrality_tolerance` temporarily
   - Check market index data quality
   - Verify beta calculations are reasonable

3. **Excessive Weight Changes**
   - Reduce `beta_optimization_max_weight_change`
   - Check for extreme beta values
   - Consider excluding problematic assets

### Debug Mode
```yaml
debug_mode: true  # Enables detailed beta projection logging
```

## Future Enhancements

### Potential Improvements
1. **Multi-factor models**: Extend beyond single-factor beta
2. **Dynamic beta**: Time-varying beta estimation
3. **Sector neutrality**: Additional constraints for sector exposure
4. **Risk budgeting**: Integrate with risk parity approaches

### Research Directions
1. **Alternative market indices**: Test different market proxies
2. **Beta forecasting**: Predictive beta models
3. **Transaction cost integration**: Include trading costs in optimization
4. **Regime detection**: Adapt beta calculation to market regimes
