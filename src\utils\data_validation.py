"""
Data unit validation utilities to ensure correct handling of exchange data formats
"""

import logging
import math
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class DataUnitValidator:
    """Validates data units and formats for exchange interactions"""

    @staticmethod
    def is_nan_or_invalid(value: Any) -> bool:
        """Check if value is NaN, None, or invalid"""
        if value is None:
            return True
        try:
            if isinstance(value, (int, float)):
                return math.isnan(value) or math.isinf(value)
            # Try to convert to float and check
            float_val = float(value)
            return math.isnan(float_val) or math.isinf(float_val)
        except (ValueError, TypeError, OverflowError):
            return True

    @staticmethod
    def clean_ohlcv_data(ohlcv_data: List[List], symbol: str) -> List[List]:
        """Remove rows with NaN or invalid values from OHLCV data"""
        if not ohlcv_data:
            return []

        cleaned_data = []
        dropped_count = 0

        for i, candle in enumerate(ohlcv_data):
            if len(candle) != 6:
                logger.debug(f"⚠️ Dropping {symbol} candle {i}: invalid format (expected 6 elements, got {len(candle)})")
                dropped_count += 1
                continue

            timestamp, open_price, high_price, low_price, close_price, volume = candle

            # Check for NaN or invalid values
            values_to_check = [timestamp, open_price, high_price, low_price, close_price, volume]
            if any(DataUnitValidator.is_nan_or_invalid(val) for val in values_to_check):
                logger.debug(f"⚠️ Dropping {symbol} candle {i}: contains NaN/invalid values {candle}")
                dropped_count += 1
                continue

            # Check for negative or zero prices/volume
            if any(val <= 0 for val in [open_price, high_price, low_price, close_price]) or volume < 0:
                logger.debug(f"⚠️ Dropping {symbol} candle {i}: invalid price/volume values {candle}")
                dropped_count += 1
                continue

            cleaned_data.append(candle)

        if dropped_count > 0:
            logger.info(f"🧹 Cleaned {symbol} OHLCV data: dropped {dropped_count} invalid rows, kept {len(cleaned_data)} rows")

        return cleaned_data

    @staticmethod
    def validate_ohlcv_data(ohlcv_data: List[List], symbol: str) -> bool:
        """Validate OHLCV data format and units

        Expected format: [timestamp, open, high, low, close, base_volume]
        - timestamp: Unix timestamp in milliseconds
        - open, high, low, close: Price in quote currency (USDT for BTCUSDT)
        - base_volume: Volume in base currency (BTC for BTCUSDT)

        Note: This method validates the structure. Use clean_ohlcv_data() first to remove NaN rows.
        """
        if not ohlcv_data:
            logger.warning(f"⚠️ Empty OHLCV data for {symbol}")
            return False

        for i, candle in enumerate(ohlcv_data):
            if len(candle) != 6:
                logger.error(f"❌ Invalid OHLCV format for {symbol} candle {i}: expected 6 elements, got {len(candle)}")
                return False

            timestamp, open_price, high_price, low_price, close_price, volume = candle

            # Validate timestamp
            if not isinstance(timestamp, (int, float)) or timestamp <= 0:
                logger.error(f"❌ Invalid timestamp for {symbol} candle {i}: {timestamp}")
                return False

            # Validate prices (NaN check should be done in clean_ohlcv_data)
            prices = [open_price, high_price, low_price, close_price]
            if not all(isinstance(p, (int, float)) and p > 0 for p in prices):
                logger.error(f"❌ Invalid prices for {symbol} candle {i}: {prices}")
                return False

            # Validate OHLC relationship
            if not (low_price <= open_price <= high_price and low_price <= close_price <= high_price):
                logger.warning(f"⚠️ Invalid OHLC relationship for {symbol} candle {i}: O={open_price}, H={high_price}, L={low_price}, C={close_price}")

            # Validate volume
            if not isinstance(volume, (int, float)) or volume < 0:
                logger.error(f"❌ Invalid volume for {symbol} candle {i}: {volume}")
                return False

        logger.debug(f"✅ OHLCV data validation passed for {symbol} ({len(ohlcv_data)} candles)")
        return True

    @staticmethod
    def clean_funding_rate_history(funding_history: List[Dict], symbol: str) -> List[Dict]:
        """Remove entries with NaN or invalid funding rates"""
        if not funding_history:
            return []

        cleaned_data = []
        dropped_count = 0

        for i, entry in enumerate(funding_history):
            funding_rate = entry.get('fundingRate')

            # Check for NaN or invalid funding rate
            if DataUnitValidator.is_nan_or_invalid(funding_rate):
                logger.debug(f"⚠️ Dropping {symbol} funding rate entry {i}: invalid funding rate {funding_rate}")
                dropped_count += 1
                continue

            # Check for reasonable funding rate range (-10% to +10%)
            try:
                rate_float = float(funding_rate)
                if abs(rate_float) > 0.1:  # 10% seems like a reasonable extreme limit for funding rates
                    logger.debug(f"⚠️ Dropping {symbol} funding rate entry {i}: extreme rate {rate_float*100:.2f}%")
                    dropped_count += 1
                    continue
            except (ValueError, TypeError):
                logger.debug(f"⚠️ Dropping {symbol} funding rate entry {i}: cannot convert to float {funding_rate}")
                dropped_count += 1
                continue

            cleaned_data.append(entry)

        if dropped_count > 0:
            logger.info(f"🧹 Cleaned {symbol} funding rate history: dropped {dropped_count} invalid entries, kept {len(cleaned_data)} entries")

        return cleaned_data

    @staticmethod
    def validate_position_size(symbol: str, size_usd: float, size_native: float, price: float) -> bool:
        """Validate position size calculations
        
        Args:
            symbol: Trading symbol (e.g., BTCUSDT)
            size_usd: Position size in USD (quote currency)
            size_native: Position size in base currency (BTC for BTCUSDT)
            price: Current price in quote currency
        """
        if size_usd <= 0:
            logger.error(f"❌ Invalid USD position size for {symbol}: {size_usd}")
            return False
        
        if size_native <= 0:
            logger.error(f"❌ Invalid native position size for {symbol}: {size_native}")
            return False
        
        if price <= 0:
            logger.error(f"❌ Invalid price for {symbol}: {price}")
            return False
        
        # Validate size relationship: size_usd = size_native * price
        calculated_usd = size_native * price
        tolerance = 0.01  # 1% tolerance for floating point precision
        
        if abs(calculated_usd - size_usd) / size_usd > tolerance:
            logger.error(f"❌ Position size mismatch for {symbol}: "
                        f"USD={size_usd:.2f}, Native={size_native:.6f}, Price={price:.2f}, "
                        f"Calculated USD={calculated_usd:.2f}")
            return False
        
        logger.debug(f"✅ Position size validation passed for {symbol}: "
                    f"${size_usd:.2f} = {size_native:.6f} * ${price:.2f}")
        return True
    
    @staticmethod
    def validate_order_parameters(symbol: str, side: str, amount: float, price: float) -> bool:
        """Validate order parameters before sending to exchange
        
        Args:
            symbol: Trading symbol (e.g., BTCUSDT)
            side: Order side ('buy' or 'sell')
            amount: Order amount in base currency (BTC for BTCUSDT)
            price: Order price in quote currency (USDT for BTCUSDT)
        """
        if side not in ['buy', 'sell']:
            logger.error(f"❌ Invalid order side for {symbol}: {side}")
            return False
        
        if amount <= 0:
            logger.error(f"❌ Invalid order amount for {symbol}: {amount}")
            return False
        
        if price <= 0:
            logger.error(f"❌ Invalid order price for {symbol}: {price}")
            return False
        
        # Check for reasonable ranges
        if amount > 1000000:  # Arbitrary large amount check
            logger.warning(f"⚠️ Very large order amount for {symbol}: {amount}")
        
        if price > 10000000:  # Arbitrary large price check
            logger.warning(f"⚠️ Very high price for {symbol}: {price}")
        
        logger.debug(f"✅ Order parameters validation passed for {symbol}: "
                    f"{side} {amount:.6f} @ {price:.6f}")
        return True
    
    @staticmethod
    def validate_funding_rate(symbol: str, funding_rate: float) -> bool:
        """Validate funding rate values

        Args:
            symbol: Trading symbol
            funding_rate: Funding rate as decimal (0.01 = 1%)
        """
        if not isinstance(funding_rate, (int, float)):
            logger.error(f"❌ Invalid funding rate type for {symbol}: {type(funding_rate)}")
            return False

        # Reasonable funding rate range: -10% to +10%
        if abs(funding_rate) > 0.1:
            logger.warning(f"⚠️ Extreme funding rate for {symbol}: {funding_rate*100:.4f}%")

        logger.debug(f"✅ Funding rate validation passed for {symbol}: {funding_rate*100:.4f}%")
        return True

    @staticmethod
    def convert_and_validate_funding_rate(symbol: str, funding_rate: Any) -> Optional[float]:
        """Convert funding rate to decimal format and validate

        Args:
            symbol: Trading symbol
            funding_rate: Funding rate in any format (decimal, percentage, string)

        Returns:
            Converted funding rate as decimal (0.01 = 1%) or None if invalid
        """
        if funding_rate is None:
            return None

        try:
            # Convert to float first
            rate_float = float(funding_rate)

            # Handle zero case
            if rate_float == 0:
                return 0.0

            # Auto-detect format based on magnitude with enhanced detection
            abs_rate = abs(rate_float)

            if abs_rate >= 100:
                # Likely basis points format (e.g., 150 for 1.5%)
                rate_decimal = rate_float / 10000
                logger.debug(f"🔄 {symbol}: Converted funding rate {rate_float} basis points to decimal {rate_decimal}")
            elif abs_rate > 1.0:
                # Likely percentage format (e.g., 1.5 for 1.5%)
                rate_decimal = rate_float / 100
                logger.debug(f"🔄 {symbol}: Converted funding rate {rate_float}% to decimal {rate_decimal}")
            else:
                # Already in decimal format (e.g., 0.015 for 1.5%)
                rate_decimal = rate_float
                logger.debug(f"🔄 {symbol}: Using funding rate as decimal {rate_decimal}")

            # Enhanced validation with better error messages
            if abs(rate_decimal) > 0.1:
                logger.error(f"❌ Funding rate out of reasonable range for {symbol}: {rate_decimal} ({abs(rate_decimal)*100:.2f}%)")
                logger.error(f"❌ Original input: {funding_rate}, converted to: {rate_decimal}")
                return None

            # Warning for very high rates
            if abs(rate_decimal) > 0.05:  # 5%
                logger.warning(f"⚠️ Very high funding rate for {symbol}: {rate_decimal} ({abs(rate_decimal)*100:.2f}%)")

            # Final validation
            if DataUnitValidator.validate_funding_rate(symbol, rate_decimal):
                return rate_decimal
            else:
                return None

        except (ValueError, TypeError, OverflowError) as e:
            logger.error(f"❌ Failed to convert funding rate for {symbol}: {funding_rate} - {e}")
            return None

    @staticmethod
    def convert_and_validate_price(symbol: str, price: Any, min_price: float = 0.0001) -> Optional[float]:
        """Convert price to float and validate

        Args:
            symbol: Trading symbol
            price: Price in any format
            min_price: Minimum valid price

        Returns:
            Converted price as float or None if invalid
        """
        if price is None:
            return None

        try:
            price_float = float(price)

            if price_float < min_price:
                logger.error(f"❌ Price too low for {symbol}: {price_float} < {min_price}")
                return None

            if price_float > 10000000:  # Arbitrary large price check
                logger.warning(f"⚠️ Very high price for {symbol}: {price_float}")

            return price_float

        except (ValueError, TypeError, OverflowError) as e:
            logger.error(f"❌ Failed to convert price for {symbol}: {price} - {e}")
            return None

    @staticmethod
    def convert_and_validate_volume(symbol: str, volume: Any, min_volume: float = 0) -> Optional[float]:
        """Convert volume to float and validate

        Args:
            symbol: Trading symbol
            volume: Volume in any format
            min_volume: Minimum valid volume

        Returns:
            Converted volume as float or None if invalid
        """
        if volume is None:
            return None

        try:
            volume_float = float(volume)

            if volume_float < min_volume:
                logger.debug(f"⚠️ Volume below minimum for {symbol}: {volume_float} < {min_volume}")
                return None

            return volume_float

        except (ValueError, TypeError, OverflowError) as e:
            logger.error(f"❌ Failed to convert volume for {symbol}: {volume} - {e}")
            return None
    
    @staticmethod
    def validate_volume_calculation(symbol: str, base_volume: float, price: float, usdt_volume: float) -> bool:
        """Validate volume conversion from base to USDT
        
        Args:
            symbol: Trading symbol
            base_volume: Volume in base currency
            price: Price in quote currency
            usdt_volume: Calculated USDT volume
        """
        if base_volume < 0:
            logger.error(f"❌ Negative base volume for {symbol}: {base_volume}")
            return False
        
        if price <= 0:
            logger.error(f"❌ Invalid price for volume calculation {symbol}: {price}")
            return False
        
        # Validate conversion: usdt_volume = base_volume * price
        calculated_usdt = base_volume * price
        tolerance = 0.01  # 1% tolerance
        
        if usdt_volume > 0 and abs(calculated_usdt - usdt_volume) / usdt_volume > tolerance:
            logger.error(f"❌ Volume conversion mismatch for {symbol}: "
                        f"Base={base_volume:.6f}, Price={price:.2f}, "
                        f"Expected USDT={calculated_usdt:.2f}, Got={usdt_volume:.2f}")
            return False
        
        logger.debug(f"✅ Volume calculation validation passed for {symbol}: "
                    f"{base_volume:.6f} * {price:.2f} = ${usdt_volume:.2f}")
        return True
    
    @staticmethod
    def log_data_units_summary(symbol: str, data: Dict[str, Any]) -> None:
        """Log a summary of data units for debugging"""
        logger.info(f"📊 Data Units Summary for {symbol}:")
        
        if 'price' in data:
            logger.info(f"   Price: {data['price']:.6f} QUOTE (USDT)")
        
        if 'size_usd' in data:
            logger.info(f"   Position Size: ${data['size_usd']:.2f} USD")
        
        if 'size_native' in data:
            logger.info(f"   Position Size: {data['size_native']:.6f} BASE")
        
        if 'volume' in data:
            logger.info(f"   Volume: {data['volume']:.2f} USDT")
        
        if 'funding_rate' in data:
            logger.info(f"   Funding Rate: {data['funding_rate']*100:.4f}%")


def validate_exchange_data_consistency(exchange_name: str) -> Dict[str, str]:
    """Return expected data units for different exchanges
    
    Returns:
        Dict mapping data types to their expected units
    """
    # Standard units for USDT perpetuals across major exchanges
    standard_units = {
        'ohlcv_volume': 'base_currency',  # BTC for BTCUSDT
        'ohlcv_price': 'quote_currency',  # USDT for BTCUSDT
        'order_amount': 'base_currency',  # BTC for BTCUSDT
        'order_price': 'quote_currency',  # USDT for BTCUSDT
        'position_size': 'base_currency',  # BTC for BTCUSDT
        'funding_rate': 'decimal',  # 0.01 = 1%
        'volume_24h': 'quote_currency'  # USDT for BTCUSDT
    }
    
    # Exchange-specific overrides (if any)
    exchange_overrides = {
        'bybit': {},  # Uses standard units
        'binance': {},  # Uses standard units
        'binanceusdm': {},  # Uses standard units
    }
    
    units = standard_units.copy()
    if exchange_name.lower() in exchange_overrides:
        units.update(exchange_overrides[exchange_name.lower()])
    
    logger.info(f"📋 Data units for {exchange_name}: {units}")
    return units
