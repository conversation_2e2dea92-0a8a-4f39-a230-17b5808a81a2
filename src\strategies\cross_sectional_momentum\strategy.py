"""
Cross-Sectional Momentum Strategy Implementation

This strategy implements momentum-based trading by:
1. Selecting universe from top 50 coins by market cap (excluding stablecoins)
2. Calculating 20-day z-score of daily close prices as momentum feature
3. Using sigmoid weighting for position allocation
4. Applying volatility targeting and 60-day rolling beta projection
"""

import logging
import numpy as np
import statistics
from typing import Dict, List, Optional, Any, Tuple
import yaml
from pathlib import Path

from strategies.base import BaseStrategy, StrategyPosition

logger = logging.getLogger(__name__)


class CrossSectionalMomentumStrategy(BaseStrategy):
    """
    Cross-Sectional Momentum Strategy
    
    Implements momentum-based trading using z-score of price returns,
    sigmoid weighting, and volatility-targeted position sizing with beta neutrality.
    """
    
    def __init__(self, data_fetcher, data_analyzer, exchange, main_config: Dict[str, Any]):
        """
        Initialize the momentum strategy with shared resources
        
        Args:
            data_fetcher: Shared data fetcher instance
            data_analyzer: Shared data analyzer instance
            exchange: Shared exchange interface
            main_config: Main configuration dictionary
        """
        # Load strategy-specific configuration
        strategy_config = self._load_strategy_config()
        
        # Initialize base strategy
        super().__init__(
            strategy_name="cross_sectional_momentum",
            config=strategy_config,
            data_fetcher=data_fetcher,
            data_analyzer=data_analyzer,
            exchange=exchange
        )
        
        # Store main config for shared parameters
        self.main_config = main_config
        
        # Extract frequently used parameters
        self.total_capital = main_config.get('total_capital_usd', 10000)
        self.exchange_name = main_config.get('exchange', 'bybit')
        
        self.logger.info(f"🏗️ Cross-Sectional Momentum strategy initialized")
        self.logger.info(f"   Total capital: ${self.total_capital:,.0f}")
        self.logger.info(f"   Exchange: {self.exchange_name}")
        self.logger.info(f"   Universe size: top {self.config.get('top_coins_by_market_cap', 50)} coins")
    
    def _load_strategy_config(self) -> Dict[str, Any]:
        """Load strategy-specific configuration from YAML file"""
        try:
            config_path = Path(__file__).parent / "config.yaml"
            
            if not config_path.exists():
                raise FileNotFoundError(f"Strategy config file not found: {config_path}")
            
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            logger.info(f"📂 Loaded momentum strategy config from {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"❌ Failed to load momentum strategy config: {e}")
            raise
    
    def _validate_config(self) -> None:
        """Validate strategy-specific configuration parameters"""
        required_params = [
            'top_coins_by_market_cap',
            'momentum_lookback_days',
            'peak_momentum_zscore',
            'target_volatility',
            'min_positions_per_leg'
        ]
        
        for param in required_params:
            if param not in self.config:
                raise ValueError(f"Missing required momentum strategy parameter: {param}")
        
        # Validate parameter ranges
        if self.config['top_coins_by_market_cap'] <= 0:
            raise ValueError("top_coins_by_market_cap must be positive")
        
        if self.config['momentum_lookback_days'] <= 0:
            raise ValueError("momentum_lookback_days must be positive")
        
        if self.config['peak_momentum_zscore'] <= 0:
            raise ValueError("peak_momentum_zscore must be positive")
        
        if self.config['target_volatility'] <= 0:
            raise ValueError("target_volatility must be positive")
        
        if self.config['min_positions_per_leg'] < 1:
            raise ValueError("min_positions_per_leg must be at least 1")
        
        self.logger.info("✅ Momentum strategy configuration validated")
    
    async def get_universe(self) -> List[str]:
        """
        Get universe of top coins by market cap, excluding stablecoins
        
        Returns:
            List of eligible symbol strings
        """
        try:
            self.logger.info("🌍 Fetching universe for momentum strategy...")
            
            # Get all available symbols from exchange
            markets = await self.exchange.fetch_markets()
            
            if not markets:
                self.logger.warning("⚠️ No markets found from exchange")
                return []
            
            # Filter for perpetual futures (USDT pairs)
            usdt_symbols = [
                market['symbol'] for market in markets.values()
                if market.get('type') == 'swap' and 
                market.get('quote') == 'USDT' and
                market.get('active', True)
            ]
            
            self.logger.info(f"📊 Found {len(usdt_symbols)} USDT perpetual symbols")
            
            # Exclude stablecoins
            if self.config.get('exclude_stablecoins', True):
                stablecoin_keywords = self.config.get('stablecoin_keywords', ['USD', 'USDT', 'USDC'])
                filtered_symbols = []
                
                for symbol in usdt_symbols:
                    base_currency = symbol.split('/')[0] if '/' in symbol else symbol.replace('USDT', '')
                    is_stablecoin = any(keyword in base_currency.upper() for keyword in stablecoin_keywords)
                    
                    if not is_stablecoin:
                        filtered_symbols.append(symbol)
                
                usdt_symbols = filtered_symbols
                self.logger.info(f"📊 After excluding stablecoins: {len(usdt_symbols)} symbols")
            
            # Get market cap data and sort by market cap
            # Note: This is a simplified implementation. In practice, you'd fetch market cap from
            # a data provider like CoinGecko or CoinMarketCap
            symbols_with_volume = []
            
            for symbol in usdt_symbols[:100]:  # Limit to top 100 for efficiency
                try:
                    ticker = await self.exchange.fetch_ticker(symbol)
                    if ticker and ticker.get('quoteVolume'):
                        symbols_with_volume.append({
                            'symbol': symbol,
                            'volume_24h': float(ticker['quoteVolume'])
                        })
                except Exception as e:
                    self.logger.debug(f"⏭️ Skipping {symbol} - failed to fetch ticker: {e}")
                    continue
            
            # Sort by 24h volume as proxy for market cap
            symbols_with_volume.sort(key=lambda x: x['volume_24h'], reverse=True)
            
            # Select top N symbols
            top_n = self.config.get('top_coins_by_market_cap', 50)
            universe = [item['symbol'] for item in symbols_with_volume[:top_n]]
            
            # Apply volume filter
            min_volume = self.config.get('min_daily_volume_usd', 5000000)
            filtered_universe = [
                item['symbol'] for item in symbols_with_volume[:top_n]
                if item['volume_24h'] >= min_volume
            ]
            
            self.logger.info(f"✅ Momentum strategy universe: {len(filtered_universe)} symbols")
            self.logger.info(f"📊 Top 10 symbols: {filtered_universe[:10]}")
            
            return filtered_universe
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get momentum strategy universe: {e}")
            raise
    
    async def calculate_features(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        Calculate momentum features (20-day z-score) for symbols
        
        Args:
            symbols: List of symbols to calculate features for
            
        Returns:
            List of enriched symbol dictionaries with momentum features
        """
        try:
            self.logger.info(f"📊 Calculating momentum features for {len(symbols)} symbols...")
            
            enriched_symbols = []
            lookback_days = self.config.get('momentum_lookback_days', 20)
            zscore_window = self.config.get('zscore_window_days', 60)
            min_observations = self.config.get('min_observations_for_zscore', 30)
            
            for symbol in symbols:
                try:
                    # Fetch OHLCV data for momentum calculation
                    ohlcv_data = await self.data_fetcher.get_cached_ohlcv(
                        symbol, '1d', zscore_window + lookback_days + 5
                    )
                    
                    if not ohlcv_data or len(ohlcv_data) < min_observations:
                        self.logger.debug(f"⏭️ Skipping {symbol} - insufficient data")
                        continue
                    
                    # Calculate momentum z-score
                    momentum_zscore = await self._calculate_momentum_zscore(
                        ohlcv_data, lookback_days, zscore_window
                    )
                    
                    if momentum_zscore is None:
                        self.logger.debug(f"⏭️ Skipping {symbol} - failed momentum calculation")
                        continue
                    
                    # Get current price for position sizing
                    current_price = ohlcv_data[-1][4]  # Close price
                    
                    # Create enriched symbol data
                    enriched_symbol = {
                        'symbol': symbol,
                        'momentum_zscore_20d': momentum_zscore,
                        'price': current_price,
                        'momentum_feature': momentum_zscore,  # Primary feature for position selection
                        'abs_momentum': abs(momentum_zscore),  # For ranking
                        'avg_volume_5d_usdt': 0  # Will be filled by volume calculation if needed
                    }
                    
                    enriched_symbols.append(enriched_symbol)
                    
                except Exception as e:
                    self.logger.debug(f"⏭️ Skipping {symbol} - error calculating features: {e}")
                    continue
            
            # Enrich with volatility and beta data using shared analyzer
            if enriched_symbols:
                enriched_symbols = await self.data_analyzer.enrich_coins_with_volatility(enriched_symbols)
            
            self.logger.info(f"✅ Successfully calculated momentum features for {len(enriched_symbols)} symbols")
            
            # Log momentum distribution if enabled
            if self.config.get('log_momentum_calculations', False) and enriched_symbols:
                momentum_scores = [coin['momentum_zscore_20d'] for coin in enriched_symbols]
                self.logger.info(f"📊 Momentum z-score distribution:")
                self.logger.info(f"   Min: {min(momentum_scores):.3f}")
                self.logger.info(f"   Max: {max(momentum_scores):.3f}")
                self.logger.info(f"   Mean: {statistics.mean(momentum_scores):.3f}")
                self.logger.info(f"   Std: {statistics.stdev(momentum_scores) if len(momentum_scores) > 1 else 0:.3f}")
            
            return enriched_symbols
            
        except Exception as e:
            self.logger.error(f"❌ Failed to calculate momentum features: {e}")
            raise

    async def _calculate_momentum_zscore(self, ohlcv_data: List[List],
                                       lookback_days: int, zscore_window: int) -> Optional[float]:
        """
        Calculate momentum z-score from OHLCV data

        Args:
            ohlcv_data: OHLCV data list
            lookback_days: Days for momentum calculation
            zscore_window: Window for z-score calculation

        Returns:
            Momentum z-score or None if calculation fails
        """
        try:
            if len(ohlcv_data) < zscore_window + lookback_days:
                return None

            # Extract close prices
            closes = [candle[4] for candle in ohlcv_data]

            # Calculate returns
            if self.config.get('use_log_returns', True):
                # Log returns
                returns = []
                for i in range(1, len(closes)):
                    if closes[i-1] > 0 and closes[i] > 0:
                        returns.append(np.log(closes[i] / closes[i-1]))
                    else:
                        returns.append(0.0)
            else:
                # Simple returns
                returns = []
                for i in range(1, len(closes)):
                    if closes[i-1] > 0:
                        returns.append((closes[i] - closes[i-1]) / closes[i-1])
                    else:
                        returns.append(0.0)

            if len(returns) < zscore_window:
                return None

            # Calculate momentum (cumulative return over lookback period)
            recent_returns = returns[-lookback_days:]
            momentum = sum(recent_returns)

            # Calculate rolling z-score
            historical_momentums = []
            for i in range(lookback_days, len(returns) - lookback_days + 1):
                period_returns = returns[i:i + lookback_days]
                period_momentum = sum(period_returns)
                historical_momentums.append(period_momentum)

            if len(historical_momentums) < 10:  # Need minimum observations
                return None

            # Calculate z-score
            mean_momentum = statistics.mean(historical_momentums)
            std_momentum = statistics.stdev(historical_momentums) if len(historical_momentums) > 1 else 0

            if std_momentum == 0:
                return 0.0

            zscore = (momentum - mean_momentum) / std_momentum

            # Handle outliers
            if self.config.get('handle_momentum_outliers', True):
                outlier_threshold = self.config.get('momentum_outlier_threshold', 5.0)
                if abs(zscore) > outlier_threshold:
                    zscore = np.sign(zscore) * outlier_threshold

            return zscore

        except Exception as e:
            self.logger.debug(f"❌ Error calculating momentum z-score: {e}")
            return None

    async def select_positions(self, enriched_symbols: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """
        Select long and short position candidates based on momentum z-scores

        Args:
            enriched_symbols: Symbols with calculated momentum features

        Returns:
            Tuple of (long_candidates, short_candidates)
        """
        try:
            self.logger.info(f"🎯 Selecting momentum positions from {len(enriched_symbols)} symbols...")

            # Get configuration parameters
            long_threshold = self.config.get('long_momentum_threshold', 0.5)
            short_threshold = self.config.get('short_momentum_threshold', -0.5)
            peak_momentum = self.config.get('peak_momentum_zscore', 2.0)
            min_positions_per_leg = self.config.get('min_positions_per_leg', 3)

            # Separate into potential longs and shorts based on momentum
            potential_longs = []
            potential_shorts = []

            for symbol_data in enriched_symbols:
                momentum_zscore = symbol_data.get('momentum_zscore_20d', 0)

                # Calculate sigmoid weight for position sizing
                sigmoid_weight = self._momentum_sigmoid_function(momentum_zscore, peak_momentum)
                symbol_data['momentum_weight'] = sigmoid_weight

                # Select based on momentum thresholds
                if momentum_zscore >= long_threshold:
                    # Positive momentum = long candidate
                    potential_longs.append(symbol_data)
                    self.logger.debug(f"📊 {symbol_data['symbol']}: Long candidate, "
                                    f"momentum={momentum_zscore:.3f}, weight={sigmoid_weight:.4f}")

                elif momentum_zscore <= short_threshold:
                    # Negative momentum = short candidate
                    potential_shorts.append(symbol_data)
                    self.logger.debug(f"📊 {symbol_data['symbol']}: Short candidate, "
                                    f"momentum={momentum_zscore:.3f}, weight={sigmoid_weight:.4f}")

            # Sort by three-tier ranking: 1) Momentum weight, 2) Absolute momentum, 3) Volume
            potential_longs.sort(
                key=lambda x: (x['momentum_weight'], x['abs_momentum'], x.get('avg_volume_5d_usdt', 0)),
                reverse=True
            )
            potential_shorts.sort(
                key=lambda x: (x['momentum_weight'], x['abs_momentum'], x.get('avg_volume_5d_usdt', 0)),
                reverse=True
            )

            # Ensure minimum positions per leg
            long_candidates = potential_longs
            short_candidates = potential_shorts

            # Handle insufficient positions (similar to StatArb strategy)
            if len(long_candidates) < min_positions_per_leg or len(short_candidates) < min_positions_per_leg:
                self.logger.warning(f"⚠️ Insufficient momentum candidates: "
                                  f"{len(long_candidates)} longs, {len(short_candidates)} shorts")

                # For momentum strategy, we can be more flexible with minimum positions
                # since momentum signals are more directional
                if len(long_candidates) == 0 and len(short_candidates) == 0:
                    self.logger.warning("⚠️ No momentum candidates found")
                    return [], []

            self.logger.info(f"✅ Selected {len(long_candidates)} long and {len(short_candidates)} short momentum candidates")

            # Log selection details if enabled
            if self.config.get('log_position_selection_details', False):
                if long_candidates:
                    self.logger.info(f"📊 Top 3 long momentum candidates:")
                    for i, candidate in enumerate(long_candidates[:3]):
                        self.logger.info(f"   {i+1}. {candidate['symbol']}: "
                                       f"momentum={candidate['momentum_zscore_20d']:.3f}, "
                                       f"weight={candidate['momentum_weight']:.4f}")

                if short_candidates:
                    self.logger.info(f"📊 Top 3 short momentum candidates:")
                    for i, candidate in enumerate(short_candidates[:3]):
                        self.logger.info(f"   {i+1}. {candidate['symbol']}: "
                                       f"momentum={candidate['momentum_zscore_20d']:.3f}, "
                                       f"weight={candidate['momentum_weight']:.4f}")

            return long_candidates, short_candidates

        except Exception as e:
            self.logger.error(f"❌ Failed to select momentum positions: {e}")
            raise

    def _momentum_sigmoid_function(self, momentum_zscore: float, peak_momentum: float) -> float:
        """
        Sigmoid mapping function for momentum-based position weighting

        Formula: y = |x| * exp(-(|x| / peak_momentum)^2)
        Where x is the momentum z-score

        Args:
            momentum_zscore: Momentum z-score (feature value)
            peak_momentum: Peak momentum z-score from config

        Returns:
            Normalized weight between 0 and 1
        """
        # Validate peak_momentum
        if peak_momentum <= 0:
            self.logger.error(f"❌ Invalid peak_momentum: {peak_momentum}. Must be positive.")
            raise ValueError(f"peak_momentum must be positive, got {peak_momentum}")

        # Validate input
        if not isinstance(momentum_zscore, (int, float)) or np.isnan(momentum_zscore) or np.isinf(momentum_zscore):
            self.logger.warning(f"⚠️ Invalid momentum z-score: {momentum_zscore}, returning 0")
            return 0.0

        try:
            # Use absolute value for sigmoid calculation (momentum strength)
            abs_momentum = abs(momentum_zscore)

            # Calculate sigmoid value with overflow protection
            ratio = abs_momentum / peak_momentum
            if ratio > 10:  # Prevent overflow
                ratio = 10

            y = abs_momentum * np.exp(-(ratio ** 2))

            # Normalize by maximum value (occurs at x = peak_momentum)
            y_max = peak_momentum * np.exp(-1)

            # Return normalized weight with bounds checking
            normalized_weight = y / y_max if y_max != 0 else 0.0

            # Ensure result is within valid bounds
            return max(0.0, min(1.0, normalized_weight))

        except (OverflowError, ZeroDivisionError) as e:
            self.logger.warning(f"⚠️ Numerical error in momentum sigmoid calculation: {e}, returning 0")
            return 0.0

    async def size_positions(self, long_candidates: List[Dict],
                           short_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Calculate position sizes using momentum weighting and volatility targeting

        Args:
            long_candidates: Selected long position candidates
            short_candidates: Selected short position candidates

        Returns:
            List of StrategyPosition objects with calculated sizes
        """
        try:
            self.logger.info(f"💰 Calculating momentum position sizes for {len(long_candidates)} longs and {len(short_candidates)} shorts...")

            # Create a temporary position manager with strategy-specific config
            merged_config = self.main_config.copy()
            merged_config.update(self.config)

            # Import here to avoid circular imports
            from execution.position_manager import PositionManager

            position_manager = PositionManager(self.exchange, self.data_analyzer, merged_config)

            # Convert momentum candidates to format expected by position manager
            # We need to add the required fields for position sizing
            processed_long_candidates = []
            for candidate in long_candidates:
                processed_candidate = candidate.copy()
                # Map momentum weight to ev_weight for compatibility with position manager
                processed_candidate['ev_weight'] = candidate['momentum_weight']
                processed_candidate['adjusted_funding'] = candidate['momentum_zscore_20d']  # Use momentum as "funding"
                processed_long_candidates.append(processed_candidate)

            processed_short_candidates = []
            for candidate in short_candidates:
                processed_candidate = candidate.copy()
                # Map momentum weight to ev_weight for compatibility with position manager
                processed_candidate['ev_weight'] = candidate['momentum_weight']
                processed_candidate['adjusted_funding'] = candidate['momentum_zscore_20d']  # Use momentum as "funding"
                processed_short_candidates.append(processed_candidate)

            # Calculate position sizes using the position manager's EV-based approach
            # (which works well for any sigmoid-weighted strategy)
            target_positions = await position_manager.calculate_position_sizes_ev_based(
                processed_long_candidates, processed_short_candidates
            )

            if not target_positions:
                self.logger.warning("⚠️ No viable momentum positions calculated")
                return []

            # Convert to StrategyPosition objects
            strategy_positions = []
            for pos in target_positions:
                strategy_position = StrategyPosition(
                    symbol=pos['symbol'],
                    side=pos['side'],
                    size_usd=pos['size_usd'],
                    size_native=pos['size_native'],
                    weight=pos.get('weight', 0.0),
                    confidence=1.0,  # Default confidence
                    metadata={
                        'momentum_zscore': pos.get('adjusted_funding', 0.0),  # Store original momentum
                        'momentum_weight': pos.get('weight', 0.0),
                        'volatility': pos.get('volatility', 0.0),
                        'leverage': pos.get('leverage', 1.0),
                        'strategy_source': 'cross_sectional_momentum'
                    }
                )
                strategy_positions.append(strategy_position)

            total_capital = sum(pos.size_usd for pos in strategy_positions)
            long_count = len([pos for pos in strategy_positions if pos.side == 'long'])
            short_count = len([pos for pos in strategy_positions if pos.side == 'short'])

            self.logger.info(f"✅ Calculated momentum position sizes for {len(strategy_positions)} positions:")
            self.logger.info(f"   Long: {long_count} positions")
            self.logger.info(f"   Short: {short_count} positions")
            self.logger.info(f"   Total capital: ${total_capital:,.0f}")

            return strategy_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to calculate momentum position sizes: {e}")
            raise

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get detailed information about this momentum strategy"""
        base_info = super().get_strategy_info()

        strategy_info = {
            **base_info,
            'description': 'Cross-Sectional Momentum Strategy',
            'strategy_type': 'momentum_based',
            'universe_selection': 'top_market_cap_excluding_stablecoins',
            'position_selection': 'momentum_zscore_with_sigmoid_weighting',
            'position_sizing': 'volatility_targeted_with_beta_neutrality',
            'risk_management': 'momentum_thresholds_and_buffer_zones',
            'exchange': self.exchange_name,
            'total_capital': self.total_capital,
            'key_parameters': {
                'top_coins_by_market_cap': self.config.get('top_coins_by_market_cap'),
                'momentum_lookback_days': self.config.get('momentum_lookback_days'),
                'peak_momentum_zscore': self.config.get('peak_momentum_zscore'),
                'target_volatility': self.config.get('target_volatility'),
                'min_positions_per_leg': self.config.get('min_positions_per_leg'),
                'long_momentum_threshold': self.config.get('long_momentum_threshold'),
                'short_momentum_threshold': self.config.get('short_momentum_threshold')
            }
        }

        return strategy_info
