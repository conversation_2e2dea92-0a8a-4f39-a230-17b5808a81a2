"""
Base strategy interface and data structures for the multi-strategy framework

This module defines the abstract base class that all trading strategies must implement,
along with common data structures for strategy results and portfolio positions.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class StrategyPosition:
    """Represents a single position in a strategy's target portfolio"""
    symbol: str
    side: str  # 'long' or 'short'
    size_usd: float
    size_native: float  # Position size in base currency
    weight: float  # Strategy weight for this position
    confidence: float = 1.0  # Strategy confidence (0-1)
    metadata: Dict[str, Any] = field(default_factory=dict)  # Strategy-specific data
    
    def __post_init__(self):
        """Validate position data after initialization"""
        if self.side not in ['long', 'short']:
            raise ValueError(f"Invalid side: {self.side}. Must be 'long' or 'short'")
        if self.size_usd < 0:
            raise ValueError(f"Invalid size_usd: {self.size_usd}. Must be non-negative")
        if not 0 <= self.confidence <= 1:
            raise ValueError(f"Invalid confidence: {self.confidence}. Must be between 0 and 1")


@dataclass
class StrategyResult:
    """Contains the complete result from a strategy execution"""
    strategy_name: str
    target_positions: List[StrategyPosition]
    total_capital_allocated: float
    execution_time_seconds: float
    universe_size: int  # Number of symbols in universe
    candidates_count: int  # Number of position candidates
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)  # Strategy-specific metrics
    timestamp: datetime = field(default_factory=lambda: datetime.utcnow())
    
    @property
    def position_count(self) -> int:
        """Total number of positions in target portfolio"""
        return len(self.target_positions)
    
    @property
    def long_positions(self) -> List[StrategyPosition]:
        """Get all long positions"""
        return [pos for pos in self.target_positions if pos.side == 'long']
    
    @property
    def short_positions(self) -> List[StrategyPosition]:
        """Get all short positions"""
        return [pos for pos in self.target_positions if pos.side == 'short']
    
    @property
    def long_capital(self) -> float:
        """Total capital allocated to long positions"""
        return sum(pos.size_usd for pos in self.long_positions)
    
    @property
    def short_capital(self) -> float:
        """Total capital allocated to short positions"""
        return sum(pos.size_usd for pos in self.short_positions)


class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies
    
    Each strategy must implement the core methods for universe selection,
    feature calculation, position selection, and portfolio construction.
    """
    
    def __init__(self, strategy_name: str, config: Dict[str, Any], 
                 data_fetcher, data_analyzer, exchange):
        """
        Initialize strategy with shared resources
        
        Args:
            strategy_name: Unique name for this strategy
            config: Strategy-specific configuration
            data_fetcher: Shared data fetcher instance
            data_analyzer: Shared data analyzer instance  
            exchange: Shared exchange interface
        """
        self.strategy_name = strategy_name
        self.config = config
        self.data_fetcher = data_fetcher
        self.data_analyzer = data_analyzer
        self.exchange = exchange
        self.logger = logging.getLogger(f"{__name__}.{strategy_name}")
        
        # Validate required config parameters
        self._validate_config()
        
        self.logger.info(f"🏗️ Initialized strategy: {strategy_name}")
    
    @abstractmethod
    def _validate_config(self) -> None:
        """Validate strategy-specific configuration parameters"""
        pass
    
    @abstractmethod
    async def get_universe(self) -> List[str]:
        """
        Get the universe of symbols this strategy will consider
        
        Returns:
            List of symbol strings (e.g., ['BTCUSDT', 'ETHUSDT'])
        """
        pass
    
    @abstractmethod
    async def calculate_features(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        Calculate strategy-specific features for the given symbols
        
        Args:
            symbols: List of symbols to calculate features for
            
        Returns:
            List of dictionaries containing symbol data with calculated features
        """
        pass
    
    @abstractmethod
    async def select_positions(self, enriched_symbols: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """
        Select long and short position candidates from enriched symbol data
        
        Args:
            enriched_symbols: Symbols with calculated features
            
        Returns:
            Tuple of (long_candidates, short_candidates)
        """
        pass
    
    @abstractmethod
    async def size_positions(self, long_candidates: List[Dict], 
                           short_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Calculate position sizes for selected candidates
        
        Args:
            long_candidates: Selected long position candidates
            short_candidates: Selected short position candidates
            
        Returns:
            List of StrategyPosition objects with calculated sizes
        """
        pass
    
    async def execute(self) -> StrategyResult:
        """
        Execute the complete strategy pipeline
        
        This is the main entry point that orchestrates the entire strategy execution:
        1. Universe selection
        2. Feature calculation  
        3. Position selection
        4. Position sizing
        
        Returns:
            StrategyResult containing the target portfolio and execution metrics
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info(f"🚀 Starting strategy execution: {self.strategy_name}")
            
            # Step 1: Get universe
            self.logger.info("🌍 Getting strategy universe...")
            universe = await self.get_universe()
            if not universe:
                self.logger.warning("⚠️ Empty universe - no symbols to trade")
                return self._create_empty_result(start_time, 0, 0, "Empty universe")
            
            self.logger.info(f"✅ Universe contains {len(universe)} symbols")
            
            # Step 2: Calculate features
            self.logger.info("📊 Calculating strategy features...")
            enriched_symbols = await self.calculate_features(universe)
            if not enriched_symbols:
                self.logger.warning("⚠️ No symbols passed feature calculation")
                return self._create_empty_result(start_time, len(universe), 0, "No viable symbols after feature calculation")
            
            self.logger.info(f"✅ {len(enriched_symbols)} symbols passed feature calculation")
            
            # Step 3: Select positions
            self.logger.info("🎯 Selecting position candidates...")
            long_candidates, short_candidates = await self.select_positions(enriched_symbols)
            total_candidates = len(long_candidates) + len(short_candidates)
            
            if total_candidates == 0:
                self.logger.warning("⚠️ No position candidates selected")
                return self._create_empty_result(start_time, len(universe), 0, "No position candidates selected")
            
            self.logger.info(f"✅ Selected {len(long_candidates)} long and {len(short_candidates)} short candidates")
            
            # Step 4: Size positions
            self.logger.info("💰 Calculating position sizes...")
            target_positions = await self.size_positions(long_candidates, short_candidates)
            
            if not target_positions:
                self.logger.warning("⚠️ No viable positions after sizing")
                return self._create_empty_result(start_time, len(universe), total_candidates, "No viable positions after sizing")
            
            # Calculate execution metrics
            execution_time = asyncio.get_event_loop().time() - start_time
            total_capital = sum(pos.size_usd for pos in target_positions)
            
            self.logger.info(f"✅ Strategy execution completed: {len(target_positions)} positions, "
                           f"${total_capital:,.0f} allocated, {execution_time:.2f}s")
            
            return StrategyResult(
                strategy_name=self.strategy_name,
                target_positions=target_positions,
                total_capital_allocated=total_capital,
                execution_time_seconds=execution_time,
                universe_size=len(universe),
                candidates_count=total_candidates,
                success=True
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"❌ Strategy execution failed: {e}")
            import traceback
            self.logger.debug(f"Full traceback: {traceback.format_exc()}")
            
            return StrategyResult(
                strategy_name=self.strategy_name,
                target_positions=[],
                total_capital_allocated=0.0,
                execution_time_seconds=execution_time,
                universe_size=0,
                candidates_count=0,
                success=False,
                error_message=str(e)
            )
    
    def _create_empty_result(self, start_time: float, universe_size: int, 
                           candidates_count: int, reason: str) -> StrategyResult:
        """Create an empty but successful result"""
        execution_time = asyncio.get_event_loop().time() - start_time
        
        return StrategyResult(
            strategy_name=self.strategy_name,
            target_positions=[],
            total_capital_allocated=0.0,
            execution_time_seconds=execution_time,
            universe_size=universe_size,
            candidates_count=candidates_count,
            success=True,
            metadata={'empty_reason': reason}
        )
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get basic information about this strategy"""
        return {
            'name': self.strategy_name,
            'config_keys': list(self.config.keys()),
            'class': self.__class__.__name__
        }
