# Multi-Strategy Trading System Architecture

## Overview

The trading system has been refactored from a single-strategy approach to a comprehensive multi-strategy framework that supports:

- **Multiple Strategy Execution**: Run multiple trading strategies in parallel
- **Portfolio Combination**: Intelligently combine and net positions from different strategies
- **Performance Attribution**: Track performance of individual strategies and the combined portfolio
- **Shared Resources**: Efficient data fetching and caching shared across all strategies
- **Modular Design**: Easy to add new strategies without modifying existing code

## Architecture Components

### 1. Strategy Framework (`src/strategies/`)

#### Base Strategy Interface (`base.py`)
- **BaseStrategy**: Abstract base class that all strategies must implement
- **StrategyPosition**: Data structure for individual positions
- **StrategyResult**: Complete result from strategy execution

#### Strategy Manager (`strategy_manager.py`)
- Coordinates execution of multiple strategies in parallel
- Handles strategy registration, validation, and lifecycle management
- Provides timeout protection and error handling

#### Individual Strategies
- **StatArb Carry Trade** (`stat_arb_carry_trade/`): Funding arbitrage strategy
- Each strategy has its own configuration file and implementation
- Future strategies can be added by implementing the BaseStrategy interface

### 2. Portfolio Management (`src/portfolio/`)

#### Portfolio Combiner (`combiner.py`)
- Combines target portfolios from multiple strategies
- Applies strategy weights to individual positions
- Nets long/short positions by symbol
- Maintains attribution to source strategies

#### Performance Tracker (`performance_tracker.py`)
- Tracks performance metrics for individual strategies
- Records combined portfolio performance
- Maintains historical data for analysis
- Exports data for external analysis (e.g., Grafana)

### 3. Core Orchestration (`src/core/`)

#### Multi-Strategy Orchestrator (`orchestrator.py`)
- Main coordination logic replacing the single-strategy approach
- Manages data fetching, strategy execution, and portfolio combination
- Handles rebalancing and monitoring cycles
- Integrates with existing execution and state management

## Data Flow

```
1. Data Fetching & Caching
   ↓
2. Parallel Strategy Execution
   ├── Strategy 1 (StatArb Carry Trade)
   ├── Strategy 2 (Future Strategy)
   └── Strategy N (Future Strategy)
   ↓
3. Portfolio Combination
   ├── Apply Strategy Weights
   ├── Net Positions by Symbol
   └── Filter Minimum Sizes
   ↓
4. Trade Execution
   ↓
5. Performance Tracking
```

## Configuration Structure

### Main Configuration (`config.yaml`)
```yaml
# Strategy weights and enablement
strategies:
  stat_arb_carry_trade:
    enabled: true
    weight: 1.0

# Shared parameters
total_capital_usd: 10000
portfolio_combination:
  enable_position_netting: true
  min_position_size_usd: 10.0

# Performance tracking
performance_tracking:
  max_performance_history_days: 90
  enable_detailed_performance_tracking: true
```

### Strategy-Specific Configuration
Each strategy has its own `config.yaml` file:
- `src/strategies/stat_arb_carry_trade/config.yaml`
- Contains strategy-specific parameters
- Universe selection criteria
- Feature calculation parameters
- Position selection and sizing logic

## Key Features

### 1. Parallel Strategy Execution
- Strategies run independently and in parallel
- Shared data cache prevents redundant API calls
- Individual timeout protection per strategy
- Graceful handling of strategy failures

### 2. Intelligent Portfolio Combination
- **Position Netting**: Long and short positions in the same symbol are netted
- **Strategy Weights**: Each strategy's positions are scaled by its portfolio weight
- **Attribution Tracking**: Maintains record of which strategies contributed to each position
- **Minimum Size Filtering**: Removes positions below minimum thresholds

### 3. Performance Attribution
- **Strategy-Level Metrics**: Track each strategy's performance independently
- **Portfolio-Level Metrics**: Track combined portfolio performance
- **Historical Data**: Maintain performance history for analysis
- **Export Capability**: Export data for external tools like Grafana

### 4. Shared Resource Efficiency
- **Data Caching**: Market data fetched once and shared across strategies
- **Exchange Connection**: Single exchange connection shared by all strategies
- **Execution Engine**: Unified execution engine for all trades

## Adding New Strategies

To add a new strategy:

1. **Create Strategy Directory**:
   ```
   src/strategies/my_new_strategy/
   ├── __init__.py
   ├── strategy.py
   └── config.yaml
   ```

2. **Implement Strategy Class**:
   ```python
   class MyNewStrategy(BaseStrategy):
       async def get_universe(self) -> List[str]:
           # Return list of symbols to trade
       
       async def calculate_features(self, symbols: List[str]) -> List[Dict]:
           # Calculate strategy-specific features
       
       async def select_positions(self, enriched_symbols: List[Dict]) -> Tuple[List, List]:
           # Select long and short candidates
       
       async def size_positions(self, long_candidates: List, short_candidates: List) -> List[StrategyPosition]:
           # Calculate position sizes
   ```

3. **Register Strategy** in orchestrator initialization:
   ```python
   if strategy_name == 'my_new_strategy':
       strategy = MyNewStrategy(...)
       self.strategy_manager.register_strategy(strategy, weight)
   ```

4. **Add Configuration** to main `config.yaml`:
   ```yaml
   strategies:
     my_new_strategy:
       enabled: true
       weight: 0.5
   ```

## Migration from Single Strategy

The refactoring maintains backward compatibility:

- **Existing Configuration**: Most parameters remain the same
- **Data Structures**: Position and execution formats unchanged
- **Execution Logic**: Same randomized execution engine
- **State Management**: Same state persistence logic

### Key Changes:
- `StatArbStrategy` → `MultiStrategyOrchestrator`
- Strategy-specific config moved to separate files
- Added strategy weights and combination logic
- Enhanced performance tracking

## Testing

Run the test suite to verify the architecture:

```bash
python test_multi_strategy.py
```

This tests:
- Strategy manager functionality
- Portfolio combination logic
- Performance tracking
- Data structure integrity

## Future Enhancements

The architecture is designed to support:

1. **Additional Strategies**: Mean reversion, momentum, pairs trading, etc.
2. **Advanced Combination Logic**: Risk parity, optimization-based allocation
3. **Real-time Performance Monitoring**: Grafana dashboards, alerts
4. **Strategy Optimization**: Hyperparameter tuning, walk-forward analysis
5. **Risk Management**: Portfolio-level risk controls, correlation monitoring

## Benefits

1. **Diversification**: Multiple strategies reduce single-strategy risk
2. **Scalability**: Easy to add new strategies without code changes
3. **Performance Attribution**: Clear understanding of strategy contributions
4. **Resource Efficiency**: Shared data fetching and execution
5. **Maintainability**: Modular design with clear separation of concerns
6. **Flexibility**: Individual strategy weights can be adjusted dynamically
