# Multi-Strategy Trading System Architecture

## Overview

The trading system has been refactored from a single-strategy approach to a comprehensive multi-strategy framework that supports:

- **Multiple Strategy Execution**: Run multiple trading strategies in parallel
- **Portfolio Combination**: Intelligently combine and net positions from different strategies
- **Performance Attribution**: Track performance of individual strategies and the combined portfolio
- **Shared Resources**: Efficient data fetching and caching shared across all strategies
- **Modular Design**: Easy to add new strategies without modifying existing code

## Architecture Components

### 1. Strategy Framework (`src/strategies/`)

#### Base Strategy Interface (`base.py`)
- **BaseStrategy**: Abstract base class that all strategies must implement
- **StrategyPosition**: Data structure for individual positions
- **StrategyResult**: Complete result from strategy execution

#### Strategy Manager (`strategy_manager.py`)
- Coordinates execution of multiple strategies in parallel
- Handles strategy registration, validation, and lifecycle management
- Provides timeout protection and error handling

#### Individual Strategies
- **StatArb Carry Trade** (`stat_arb_carry_trade/`): Funding arbitrage strategy
- Each strategy has its own configuration file and implementation
- Future strategies can be added by implementing the BaseStrategy interface

### 2. Portfolio Management (`src/portfolio/`)

#### Portfolio Combiner (`combiner.py`)
- Combines target portfolios from multiple strategies
- Applies strategy weights to individual positions
- Nets long/short positions by symbol
- Maintains attribution to source strategies

#### Performance Tracker (`performance_tracker.py`)
- Tracks performance metrics for individual strategies
- Records combined portfolio performance
- Maintains historical data for analysis
- Exports data for external analysis (e.g., Grafana)

### 3. Core Orchestration (`src/core/`)

#### Multi-Strategy Orchestrator (`orchestrator.py`)
- Main coordination logic replacing the single-strategy approach
- Manages data fetching, strategy execution, and portfolio combination
- Handles rebalancing and monitoring cycles
- Integrates with existing execution and state management

## Data Flow

```
1. Data Fetching & Caching
   ↓
2. Parallel Strategy Execution
   ├── Strategy 1 (StatArb Carry Trade)
   ├── Strategy 2 (Future Strategy)
   └── Strategy N (Future Strategy)
   ↓
3. Portfolio Combination
   ├── Apply Strategy Weights
   ├── Net Positions by Symbol
   └── Filter Minimum Sizes
   ↓
4. Trade Execution
   ↓
5. Performance Tracking
```

## Configuration Structure

### Main Configuration (`config.yaml`)
```yaml
# Strategy weights and enablement
strategies:
  stat_arb_carry_trade:
    enabled: true
    weight: 1.0

# Shared parameters
total_capital_usd: 10000
portfolio_combination:
  enable_position_netting: true
  min_position_size_usd: 10.0

# Performance tracking
performance_tracking:
  max_performance_history_days: 90
  enable_detailed_performance_tracking: true
```

### Strategy-Specific Configuration
Each strategy has its own `config.yaml` file:
- `src/strategies/stat_arb_carry_trade/config.yaml`
- Contains strategy-specific parameters
- Universe selection criteria
- Feature calculation parameters
- Position selection and sizing logic

## Key Features

### 1. Parallel Strategy Execution
- Strategies run independently and in parallel
- Shared data cache prevents redundant API calls
- Individual timeout protection per strategy
- Graceful handling of strategy failures

### 2. Intelligent Portfolio Combination
- **Position Netting**: Long and short positions in the same symbol are netted
- **Strategy Weights**: Each strategy's positions are scaled by its portfolio weight
- **Attribution Tracking**: Maintains record of which strategies contributed to each position
- **Minimum Size Filtering**: Removes positions below minimum thresholds

### 3. Performance Attribution
- **Strategy-Level Metrics**: Track each strategy's performance independently
- **Portfolio-Level Metrics**: Track combined portfolio performance
- **Historical Data**: Maintain performance history for analysis
- **Export Capability**: Export data for external tools like Grafana

### 4. Shared Resource Efficiency
- **Data Caching**: Market data fetched once and shared across strategies
- **Exchange Connection**: Single exchange connection shared by all strategies
- **Execution Engine**: Unified execution engine for all trades

## Adding New Strategies

### Step-by-Step Guide

To add a new strategy, follow these steps:

1. **Create Strategy Directory Structure**:
   ```
   src/strategies/my_new_strategy/
   ├── __init__.py
   ├── strategy.py
   └── config.yaml
   ```

2. **Implement Strategy Class** (inherit from `BaseStrategy`):
   ```python
   from strategies.base import BaseStrategy, StrategyPosition

   class MyNewStrategy(BaseStrategy):
       async def get_universe(self) -> List[str]:
           # Return list of symbols to trade

       async def calculate_features(self, symbols: List[str]) -> List[Dict]:
           # Calculate strategy-specific features

       async def select_positions(self, enriched_symbols: List[Dict]) -> Tuple[List, List]:
           # Select long and short candidates

       async def size_positions(self, long_candidates: List, short_candidates: List) -> List[StrategyPosition]:
           # Calculate position sizes and return StrategyPosition objects
   ```

3. **Register Strategy** in orchestrator (`src/core/orchestrator.py`):
   ```python
   elif strategy_name == 'my_new_strategy':
       strategy = MyNewStrategy(
           data_fetcher=self.data_fetcher,
           data_analyzer=self.data_analyzer,
           exchange=self.exchange,
           main_config=self.config.to_dict()
       )
       self.strategy_manager.register_strategy(strategy, weight)
   ```

4. **Add Configuration** to main `config.yaml`:
   ```yaml
   strategies:
     my_new_strategy:
       enabled: true
       weight: 0.5
   ```

### Complete Example: Cross-Sectional Momentum Strategy

Here's a complete implementation of a momentum-based strategy:

#### Strategy Requirements:
- **Universe**: Top 50 coins by market cap (excluding stablecoins)
- **Feature**: 20-day z-score of daily close price returns
- **Weighting**: Sigmoid function for position allocation
- **Sizing**: Volatility targeting with 60-day rolling beta projection

#### 1. Directory Structure:
```
src/strategies/cross_sectional_momentum/
├── __init__.py
├── strategy.py
└── config.yaml
```

#### 2. Configuration File (`config.yaml`):
```yaml
# Cross-Sectional Momentum Strategy Configuration

# Universe Selection
top_coins_by_market_cap: 50
min_daily_volume_usd: 5000000
exclude_stablecoins: true
stablecoin_keywords: ["USD", "USDT", "USDC", "BUSD", "DAI"]

# Feature Calculation
momentum_lookback_days: 20
zscore_window_days: 60
use_log_returns: true
price_field: "close"

# Position Selection
momentum_threshold: 0.5
peak_momentum_zscore: 2.0
long_momentum_threshold: 0.5
short_momentum_threshold: -0.5
min_positions_per_leg: 3

# Position Sizing
max_position_capital_pct: 20
target_volatility: 0.25
enable_beta_projection: true
beta_calculation_days: 60
min_leverage: 0.1
max_leverage: 3.0

# Risk Management
max_momentum_exposure: 0.8
buffer_zone_tolerance_percentage: 7.5
momentum_exit_threshold: 0.1
```

#### 3. Strategy Implementation (`strategy.py`):
```python
import numpy as np
import statistics
from strategies.base import BaseStrategy, StrategyPosition

class CrossSectionalMomentumStrategy(BaseStrategy):

    async def get_universe(self) -> List[str]:
        """Get top 50 coins by market cap, excluding stablecoins"""
        # Fetch all markets
        markets = await self.exchange.fetch_markets()
        usdt_symbols = [m['symbol'] for m in markets.values()
                       if m.get('type') == 'swap' and m.get('quote') == 'USDT']

        # Exclude stablecoins
        stablecoin_keywords = self.config.get('stablecoin_keywords', [])
        filtered_symbols = [s for s in usdt_symbols
                           if not any(kw in s.upper() for kw in stablecoin_keywords)]

        # Sort by volume (proxy for market cap) and take top N
        symbols_with_volume = []
        for symbol in filtered_symbols[:100]:
            ticker = await self.exchange.fetch_ticker(symbol)
            if ticker and ticker.get('quoteVolume'):
                symbols_with_volume.append({
                    'symbol': symbol,
                    'volume_24h': float(ticker['quoteVolume'])
                })

        symbols_with_volume.sort(key=lambda x: x['volume_24h'], reverse=True)
        top_n = self.config.get('top_coins_by_market_cap', 50)
        return [item['symbol'] for item in symbols_with_volume[:top_n]]

    async def calculate_features(self, symbols: List[str]) -> List[Dict]:
        """Calculate 20-day momentum z-score for each symbol"""
        enriched_symbols = []
        lookback_days = self.config.get('momentum_lookback_days', 20)
        zscore_window = self.config.get('zscore_window_days', 60)

        for symbol in symbols:
            # Fetch OHLCV data
            ohlcv_data = await self.data_fetcher.get_cached_ohlcv(
                symbol, '1d', zscore_window + lookback_days + 5
            )

            if not ohlcv_data or len(ohlcv_data) < 30:
                continue

            # Calculate momentum z-score
            momentum_zscore = await self._calculate_momentum_zscore(
                ohlcv_data, lookback_days, zscore_window
            )

            if momentum_zscore is not None:
                enriched_symbols.append({
                    'symbol': symbol,
                    'momentum_zscore_20d': momentum_zscore,
                    'momentum_feature': momentum_zscore,
                    'abs_momentum': abs(momentum_zscore),
                    'price': ohlcv_data[-1][4]  # Close price
                })

        # Enrich with volatility and beta using shared analyzer
        return await self.data_analyzer.enrich_coins_with_volatility(enriched_symbols)

    async def _calculate_momentum_zscore(self, ohlcv_data, lookback_days, zscore_window):
        """Calculate momentum z-score from price returns"""
        closes = [candle[4] for candle in ohlcv_data]

        # Calculate log returns
        returns = []
        for i in range(1, len(closes)):
            if closes[i-1] > 0 and closes[i] > 0:
                returns.append(np.log(closes[i] / closes[i-1]))

        if len(returns) < zscore_window:
            return None

        # Calculate momentum (cumulative return over lookback period)
        recent_returns = returns[-lookback_days:]
        momentum = sum(recent_returns)

        # Calculate rolling z-score
        historical_momentums = []
        for i in range(lookback_days, len(returns) - lookback_days + 1):
            period_returns = returns[i:i + lookback_days]
            historical_momentums.append(sum(period_returns))

        if len(historical_momentums) < 10:
            return None

        mean_momentum = statistics.mean(historical_momentums)
        std_momentum = statistics.stdev(historical_momentums)

        if std_momentum == 0:
            return 0.0

        return (momentum - mean_momentum) / std_momentum

    async def select_positions(self, enriched_symbols: List[Dict]) -> Tuple[List, List]:
        """Select positions based on momentum z-scores"""
        long_threshold = self.config.get('long_momentum_threshold', 0.5)
        short_threshold = self.config.get('short_momentum_threshold', -0.5)
        peak_momentum = self.config.get('peak_momentum_zscore', 2.0)

        potential_longs = []
        potential_shorts = []

        for symbol_data in enriched_symbols:
            momentum_zscore = symbol_data.get('momentum_zscore_20d', 0)

            # Calculate sigmoid weight
            sigmoid_weight = self._momentum_sigmoid_function(momentum_zscore, peak_momentum)
            symbol_data['momentum_weight'] = sigmoid_weight

            # Select based on thresholds
            if momentum_zscore >= long_threshold:
                potential_longs.append(symbol_data)
            elif momentum_zscore <= short_threshold:
                potential_shorts.append(symbol_data)

        # Sort by momentum weight, then absolute momentum, then volume
        potential_longs.sort(
            key=lambda x: (x['momentum_weight'], x['abs_momentum'],
                          x.get('avg_volume_5d_usdt', 0)), reverse=True
        )
        potential_shorts.sort(
            key=lambda x: (x['momentum_weight'], x['abs_momentum'],
                          x.get('avg_volume_5d_usdt', 0)), reverse=True
        )

        return potential_longs, potential_shorts

    def _momentum_sigmoid_function(self, momentum_zscore: float, peak_momentum: float) -> float:
        """Sigmoid weighting function: y = |x| * exp(-(|x|/peak)^2)"""
        abs_momentum = abs(momentum_zscore)
        ratio = abs_momentum / peak_momentum
        y = abs_momentum * np.exp(-(ratio ** 2))
        y_max = peak_momentum * np.exp(-1)
        return y / y_max if y_max != 0 else 0.0

    async def size_positions(self, long_candidates: List, short_candidates: List) -> List[StrategyPosition]:
        """Calculate position sizes using volatility targeting"""
        # Use shared position manager with strategy config
        merged_config = self.main_config.copy()
        merged_config.update(self.config)

        from execution.position_manager import PositionManager
        position_manager = PositionManager(self.exchange, self.data_analyzer, merged_config)

        # Convert momentum weights to format expected by position manager
        for candidate in long_candidates + short_candidates:
            candidate['ev_weight'] = candidate['momentum_weight']
            candidate['adjusted_funding'] = candidate['momentum_zscore_20d']

        # Calculate position sizes
        target_positions = await position_manager.calculate_position_sizes_ev_based(
            long_candidates, short_candidates
        )

        # Convert to StrategyPosition objects
        strategy_positions = []
        for pos in target_positions:
            strategy_position = StrategyPosition(
                symbol=pos['symbol'],
                side=pos['side'],
                size_usd=pos['size_usd'],
                size_native=pos['size_native'],
                weight=pos.get('weight', 0.0),
                confidence=1.0,
                metadata={
                    'momentum_zscore': pos.get('adjusted_funding', 0.0),
                    'momentum_weight': pos.get('weight', 0.0),
                    'volatility': pos.get('volatility', 0.0),
                    'strategy_source': 'cross_sectional_momentum'
                }
            )
            strategy_positions.append(strategy_position)

        return strategy_positions
```

#### 4. Register in Orchestrator:
Add to `src/core/orchestrator.py` in the `_initialize_strategies` method:
```python
elif strategy_name == 'cross_sectional_momentum':
    from strategies.cross_sectional_momentum import CrossSectionalMomentumStrategy
    strategy = CrossSectionalMomentumStrategy(
        data_fetcher=self.data_fetcher,
        data_analyzer=self.data_analyzer,
        exchange=self.exchange,
        main_config=self.config.to_dict()
    )
    self.strategy_manager.register_strategy(strategy, weight)
```

#### 5. Enable in Main Config:
Add to `config.yaml`:
```yaml
strategies:
  stat_arb_carry_trade:
    enabled: true
    weight: 0.6
  cross_sectional_momentum:
    enabled: true
    weight: 0.4
```

### Key Implementation Notes:

1. **Shared Resources**: All strategies share the same data fetcher, analyzer, and exchange connection
2. **Position Manager Integration**: Reuse existing position sizing logic by mapping strategy features to expected format
3. **Configuration Separation**: Strategy-specific parameters in separate config files
4. **Error Handling**: Robust error handling with graceful degradation
5. **Performance Tracking**: Automatic performance attribution through the framework
6. **Volatility Targeting**: Leverage existing volatility and beta projection logic

## Migration from Single Strategy

The refactoring maintains backward compatibility:

- **Existing Configuration**: Most parameters remain the same
- **Data Structures**: Position and execution formats unchanged
- **Execution Logic**: Same randomized execution engine
- **State Management**: Same state persistence logic

### Key Changes:
- `StatArbStrategy` → `MultiStrategyOrchestrator`
- Strategy-specific config moved to separate files
- Added strategy weights and combination logic
- Enhanced performance tracking

## Testing

Run the comprehensive test suite to verify the architecture:

```bash
python tests/test_multi_strategy_system.py
```

This tests:
- Strategy manager functionality
- Portfolio combination logic with position netting
- Performance tracking and attribution
- Configuration handling and validation
- StatArb strategy integration
- Error handling and edge cases
- Data structure integrity

## Future Enhancements

The architecture is designed to support:

1. **Additional Strategies**: Mean reversion, momentum, pairs trading, etc.
2. **Advanced Combination Logic**: Risk parity, optimization-based allocation
3. **Real-time Performance Monitoring**: Grafana dashboards, alerts
4. **Strategy Optimization**: Hyperparameter tuning, walk-forward analysis
5. **Risk Management**: Portfolio-level risk controls, correlation monitoring

## Benefits

1. **Diversification**: Multiple strategies reduce single-strategy risk
2. **Scalability**: Easy to add new strategies without code changes
3. **Performance Attribution**: Clear understanding of strategy contributions
4. **Resource Efficiency**: Shared data fetching and execution
5. **Maintainability**: Modular design with clear separation of concerns
6. **Flexibility**: Individual strategy weights can be adjusted dynamically
