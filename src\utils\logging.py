"""
Secure logging utilities with credential filtering
"""

import logging
import re
import sys
from pathlib import Path
from typing import Any


class SecureFormatter(logging.Formatter):
    """Custom formatter that filters sensitive information"""
    
    # Patterns to detect and mask sensitive information
    SENSITIVE_PATTERNS = [
        (re.compile(r'(api[_-]?key["\']?\s*[:=]\s*["\']?)([a-zA-Z0-9]{20,})', re.IGNORECASE), r'\1***MASKED***'),
        (re.compile(r'(secret["\']?\s*[:=]\s*["\']?)([a-zA-Z0-9]{20,})', re.IGNORECASE), r'\1***MASKED***'),
        (re.compile(r'(password["\']?\s*[:=]\s*["\']?)([^\s"\']{8,})', re.IGNORECASE), r'\1***MASKED***'),
        (re.compile(r'(token["\']?\s*[:=]\s*["\']?)([a-zA-Z0-9]{20,})', re.IGNORECASE), r'\1***MASKED***'),
        (re.compile(r'(["\']apiKey["\']?\s*[:=]\s*["\']?)([a-zA-Z0-9]{20,})', re.IGNORECASE), r'\1***MASKED***'),
        (re.compile(r'(["\']secret["\']?\s*[:=]\s*["\']?)([a-zA-Z0-9]{20,})', re.IGNORECASE), r'\1***MASKED***'),
    ]
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with sensitive data filtering"""
        # Get the original formatted message
        formatted = super().format(record)
        
        # Apply sensitive data masking
        for pattern, replacement in self.SENSITIVE_PATTERNS:
            formatted = pattern.sub(replacement, formatted)
        
        return formatted


def setup_logging(debug_mode: bool = False, log_file: str = "statarb_carry_trade.log") -> None:
    """Setup secure logging with credential filtering"""

    # Set log level based on debug mode
    log_level = logging.DEBUG if debug_mode else logging.INFO

    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    if not log_path.is_absolute():
        # If relative path, put in logs directory
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        log_path = logs_dir / log_file
    else:
        # Ensure parent directory exists for absolute paths
        log_path.parent.mkdir(parents=True, exist_ok=True)

    # Create secure formatter
    formatter = SecureFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Remove existing handlers to avoid duplicates
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # File handler with secure formatting
    file_handler = logging.FileHandler(str(log_path), encoding='utf-8')
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    
    # Console handler with secure formatting
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Set specific logger levels for noisy libraries
    logging.getLogger('ccxt').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"🔧 Logging configured: level={log_level}, file={log_path}, debug={debug_mode}")


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with secure formatting"""
    return logging.getLogger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class"""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class"""
        return logging.getLogger(self.__class__.__name__)


def mask_sensitive_data(data: Any) -> Any:
    """Recursively mask sensitive data in dictionaries and strings"""
    if isinstance(data, dict):
        masked = {}
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in ['key', 'secret', 'password', 'token']):
                masked[key] = '***MASKED***' if value else value
            else:
                masked[key] = mask_sensitive_data(value)
        return masked
    elif isinstance(data, list):
        return [mask_sensitive_data(item) for item in data]
    elif isinstance(data, str):
        # Apply string-based masking
        for pattern, replacement in SecureFormatter.SENSITIVE_PATTERNS:
            data = pattern.sub(replacement, data)
        return data
    else:
        return data
