"""
Main strategy orchestration and coordination
"""

import asyncio
import logging
from datetime import datetime, timezone

from config import Config
from exchanges import ExchangeFactory
from storage import StateManager
from utils import PerformanceMonitor, get_next_monitoring_time, get_next_rebalance_time, calculate_sleep_time, shutdown_coordinator
from utils.error_handler import <PERSON><PERSON>rRecoveryManager, ErrorClassifier
from data import DataFetcher, DataAnalyzer, DataCache
from execution import PositionManager
from execution.randomized_executor import RandomizedExecutor

logger = logging.getLogger(__name__)


class StatArbStrategy:
    """Legacy single-strategy orchestration class (deprecated - use MultiStrategyOrchestrator)"""
    
    def __init__(self, config: Config):
        self.config = config
        self.exchange = None
        self.state_manager = StateManager()
        self.performance_monitor = PerformanceMonitor()
        self.error_manager = ErrorRecoveryManager(config)
        self.running = False

        # Initialize data and execution modules
        self.data_cache = DataCache()
        self.data_fetcher = None
        self.data_analyzer = None
        self.position_manager = None
        self.executor = None

        logger.info("🏗️ Legacy StatArb strategy initialized (deprecated - use MultiStrategyOrchestrator)")
    
    async def initialize(self):
        """Initialize exchange connection and validate setup"""
        try:
            # Create exchange instance
            exchange_name = self.config.get('exchange', 'bybit')
            self.exchange = ExchangeFactory.create_exchange(exchange_name)
            
            # Get exchange-specific configuration
            exchange_config = self.config.get_exchange_config(exchange_name)
            
            # Initialize the exchange
            success = await self.exchange.initialize(exchange_config)
            if not success:
                raise Exception(f"Failed to initialize {exchange_name} exchange")
            
            logger.info(f"✅ Successfully initialized {exchange_name} exchange")

            # Initialize data and execution modules
            self.data_fetcher = DataFetcher(self.exchange, self.config.to_dict(), self.data_cache, self.performance_monitor)
            self.data_analyzer = DataAnalyzer(self.data_fetcher, self.config.to_dict())
            self.position_manager = PositionManager(self.exchange, self.data_analyzer, self.config.to_dict())
            self.executor = RandomizedExecutor(self.exchange, self.config.to_dict())

            logger.info("✅ Initialized data fetcher, analyzer, and randomized execution modules")

            # Load existing state
            state_summary = self.state_manager.get_summary()
            logger.info(f"📁 Loaded state: {state_summary}")

            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize strategy: {e}")
            raise
    
    async def run(self):
        """Main strategy execution loop"""
        self.running = True
        logger.info("🚀 Starting strategy execution loop")

        # Check immediate_start setting
        immediate_start = self.config.get('immediate_start', True)

        if not immediate_start:
            # Wait until next 23:00 UTC before starting
            next_start_time = get_next_rebalance_time()
            now = datetime.now(timezone.utc)
            wait_seconds = (next_start_time - now).total_seconds()

            if wait_seconds > 0:
                logger.info(f"⏰ immediate_start=False: Waiting {wait_seconds:.0f} seconds until {next_start_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")
                await asyncio.sleep(wait_seconds)
                logger.info("⏰ Start time reached - beginning strategy execution")

        try:
            while self.running and not shutdown_coordinator.is_shutdown_requested():
                # Check if it's time for rebalancing
                if await self._should_rebalance():
                    logger.info("⚖️ Starting rebalancing process...")
                    await self._rebalance_portfolio()

                # Check if it's time for monitoring
                if await self._should_monitor():
                    logger.info("👁️ Starting monitoring check...")
                    await self._monitor_positions()

                # Calculate sleep time until next event
                sleep_time = self._calculate_next_sleep()
                max_sleep = self.config.get('max_sleep_seconds', 3600)
                default_sleep = self.config.get('default_sleep_seconds', 3600)

                if sleep_time > 0:
                    actual_sleep = min(sleep_time, max_sleep)
                    logger.info(f"😴 Sleeping for {actual_sleep:.1f} seconds until next event")

                    # Sleep in smaller chunks to check for shutdown signals
                    sleep_chunk = 10.0  # Check every 10 seconds
                    remaining_sleep = actual_sleep

                    while remaining_sleep > 0 and not shutdown_coordinator.is_shutdown_requested():
                        chunk_sleep = min(sleep_chunk, remaining_sleep)
                        await asyncio.sleep(chunk_sleep)
                        remaining_sleep -= chunk_sleep
                else:
                    logger.info(f"😴 No events scheduled - sleeping for {default_sleep:.1f} seconds")

                    # Sleep in smaller chunks to check for shutdown signals
                    sleep_chunk = 10.0  # Check every 10 seconds
                    remaining_sleep = default_sleep

                    while remaining_sleep > 0 and not shutdown_coordinator.is_shutdown_requested():
                        chunk_sleep = min(sleep_chunk, remaining_sleep)
                        await asyncio.sleep(chunk_sleep)
                        remaining_sleep -= chunk_sleep
                
        except KeyboardInterrupt:
            logger.info("⏹️ Strategy stopped by user")
        except Exception as e:
            logger.error(f"❌ Strategy execution failed: {e}")
            raise
        finally:
            self.running = False
            if shutdown_coordinator.is_shutdown_requested():
                logger.info("🛑 Shutdown requested - skipping strategy cleanup (handled by shutdown coordinator)")
            else:
                await self._cleanup()
    
    async def _should_rebalance(self) -> bool:
        """Check if it's time to rebalance (at 23:00 UTC daily)"""
        now = datetime.now(timezone.utc)
        last_rebalance = self.state_manager.get_last_rebalance()

        # If immediate_start is enabled and no rebalance today, rebalance now
        immediate_start = self.config.get('immediate_start', False)
        if immediate_start:
            if not last_rebalance:
                logger.info("🚀 immediate_start=True: No previous rebalance - starting now")
                return True

            # Check if we haven't rebalanced today
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            if last_rebalance < today_start:
                logger.info("🚀 immediate_start=True: No rebalance today - starting now")
                return True

        # If no previous rebalance, rebalance now
        if not last_rebalance:
            return True

        # Check if we're at or past 23:00 and haven't rebalanced today
        if now.hour >= 23:
            # Check if last rebalance was before today's 23:00
            today_rebalance_time = now.replace(hour=23, minute=0, second=0, microsecond=0)
            return last_rebalance < today_rebalance_time

        return False
    
    async def _should_monitor(self) -> bool:
        """Check if it's time for monitoring"""
        next_monitoring = get_next_monitoring_time()
        now = datetime.now(timezone.utc)

        # Monitor if we're within the configured window of monitoring time
        monitoring_window = self.config.get('monitoring_window_seconds', 300)
        time_diff = abs((next_monitoring - now).total_seconds())
        return time_diff <= monitoring_window
    
    def _calculate_next_sleep(self) -> float:
        """Calculate sleep time until next event"""
        now = datetime.now(timezone.utc)

        # Calculate time until next rebalance (next 23:00)
        next_rebalance = get_next_rebalance_time()
        time_to_rebalance = (next_rebalance - now).total_seconds()

        # Calculate time until next monitoring
        next_monitoring = get_next_monitoring_time()
        time_to_monitoring = calculate_sleep_time(next_monitoring)

        # Return minimum time (next event)
        return max(0, min(time_to_rebalance, time_to_monitoring))
    
    async def _rebalance_portfolio(self):
        """Rebalance portfolio positions using full strategy logic"""
        try:
            timer_id = self.performance_monitor.start_timer("rebalance")

            logger.info("🔄 Starting portfolio rebalancing...")

            # Step 1: Get eligible coins
            logger.info("📡 Fetching eligible coins...")
            eligible_coins = await self.data_fetcher.get_eligible_coins()

            if not eligible_coins:
                logger.warning("⚠️ No eligible coins found - skipping rebalancing")
                return

            logger.info(f"✅ Found {len(eligible_coins)} eligible coins")

            # Step 2: Enrich with volatility data and filter
            logger.info("📊 Calculating volatility and filtering coins...")
            enriched_coins = await self.data_analyzer.enrich_coins_with_volatility(eligible_coins)

            if not enriched_coins:
                logger.warning("⚠️ No coins passed volatility filtering - skipping rebalancing")
                return

            logger.info(f"✅ {len(enriched_coins)} coins passed volatility filtering")

            # Step 3: Fetch current positions for cost adjustment logic
            logger.info("📡 Fetching current positions for cost adjustment...")
            try:
                current_positions_list = await self.exchange.fetch_positions()
                current_positions = {}
                for pos in current_positions_list:
                    if pos.get('contracts', 0) != 0:  # Only non-zero positions
                        symbol = pos['symbol']
                        current_positions[symbol] = pos
                logger.info(f"📊 Found {len(current_positions)} currently held positions")
            except Exception as e:
                logger.warning(f"⚠️ Failed to fetch current positions: {e}")
                current_positions = {}

            # Step 4: Select positions using EV-based approach (with cost adjustment logic)
            logger.info("⚖️ Selecting positions using EV-based approach...")
            long_candidates, short_candidates = self.data_analyzer.select_positions_ev_based(
                enriched_coins, current_positions)

            # Check if we have any candidates at all (empty means both legs insufficient)
            if not long_candidates and not short_candidates:
                logger.warning(f"⚠️ No viable candidates found for either leg - skipping rebalancing")
                return

            logger.info(f"📊 Selected {len(long_candidates)} long and {len(short_candidates)} short candidates")

            # Step 5: Calculate position sizes using EV-based weighting
            logger.info("💰 Calculating EV-based position sizes with sigmoid weighting...")
            target_positions = await self.position_manager.calculate_position_sizes_ev_based(long_candidates, short_candidates)

            if not target_positions:
                logger.warning("⚠️ No viable positions calculated - skipping rebalancing")
                return

            # Step 6: Apply comprehensive buffer zones to prevent over-trading
            logger.info("🎯 Applying comprehensive position buffer zones...")
            all_positions_to_execute = self.position_manager.apply_position_buffer_zones(target_positions, current_positions)

            if not all_positions_to_execute:
                logger.info("✅ All positions within buffer zones - no trades needed")
                return

            logger.info(f"📊 Buffer zones determined {len(all_positions_to_execute)} total trades needed")

            # Step 7: Execute position changes using randomized batch execution
            if all_positions_to_execute:
                if not self.config.get('simulation_mode', True):
                    logger.info("📋 Executing position changes with randomized batching...")
                    success = await self.executor.execute_to_target_positions(all_positions_to_execute)

                    if success:
                        logger.info("✅ Position changes executed successfully")
                        # Update state with target positions (excluding closed positions)
                        closed_symbols = {pos['symbol'] for pos in all_positions_to_execute if pos.get('buffer_action') == 'close_position'}
                        current_positions = {pos['symbol']: pos for pos in target_positions if pos['symbol'] not in closed_symbols}
                        self.state_manager.set_positions(current_positions)
                    else:
                        logger.error("❌ Position execution failed - NOT updating rebalance timestamp")
                        logger.error("❌ Rebalancing will be retried on next cycle")
                        # Don't update rebalance time so it will retry
                        return
                else:
                    logger.info("🎭 Simulation mode - position changes not executed")
                    # In simulation mode, update state with target positions (excluding closed positions)
                    closed_symbols = {pos['symbol'] for pos in all_positions_to_execute if pos.get('buffer_action') == 'close_position'}
                    current_positions = {pos['symbol']: pos for pos in target_positions if pos['symbol'] not in closed_symbols}
                    self.state_manager.set_positions(current_positions)
            else:
                logger.info("✅ No trades needed - all positions within buffer zones")

            # Update rebalance time
            self.state_manager.set_last_rebalance(datetime.now(timezone.utc))
            self.state_manager.save_state()

            # Log performance summary
            self.performance_monitor.end_timer(timer_id)
            self.data_cache.log_stats()

            logger.info("✅ Portfolio rebalancing completed successfully")

        except Exception as e:
            logger.error(f"❌ Rebalancing failed: {e}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            raise
    
    async def _monitor_positions(self):
        """Monitor current positions from exchange"""
        try:
            timer_id = self.performance_monitor.start_timer("monitoring")

            logger.info("👁️ Starting position monitoring...")

            # Fetch current positions from exchange
            try:
                positions = await self.exchange.fetch_positions()
                active_positions = [pos for pos in positions if pos.get('contracts', 0) != 0]

                if active_positions:
                    logger.info(f"📊 Found {len(active_positions)} active positions:")
                    for pos in active_positions:
                        symbol = pos['symbol']
                        size = pos['contracts']
                        side = pos['side']
                        logger.info(f"   {symbol}: {side} {size:.6f}")
                else:
                    logger.info("📊 No active positions found")

            except Exception as e:
                logger.warning(f"⚠️ Failed to fetch positions: {e}")

            # Check if positions match target (optional - can be enabled via config)
            if self.config.get('monitor_position_alignment', False):
                logger.info("🎯 Checking position alignment with targets...")
                positions_aligned = await self.check_positions_match_target()
                if not positions_aligned:
                    logger.warning("⚠️ Positions are not aligned with targets - may need rebalancing")

            # Update monitoring time
            self.state_manager.set_last_monitoring_check(datetime.now(timezone.utc))
            self.state_manager.save_state()

            self.performance_monitor.end_timer(timer_id)
            logger.info("✅ Position monitoring completed")

        except Exception as e:
            logger.error(f"❌ Monitoring failed: {e}")
            import traceback
            logger.debug(f"Full monitoring error traceback: {traceback.format_exc()}")
            # Don't raise - monitoring failures shouldn't stop the strategy

    async def check_positions_match_target(self, target_positions: list = None) -> bool:
        """
        Check if current positions match target positions within tolerance

        Args:
            target_positions: List of target position dictionaries. If None, generates new targets.

        Returns:
            bool: True if positions match within tolerance, False otherwise
        """
        try:
            logger.info("🎯 Checking if current positions match target...")

            # Generate target positions if not provided
            if target_positions is None:
                logger.info("📊 Generating fresh target positions for comparison...")
                target_positions = await self.position_manager.generate_target_positions()

                if not target_positions:
                    logger.warning("⚠️ No target positions generated - considering as match")
                    return True

            # Fetch current positions from exchange
            current_positions = await self.exchange.fetch_positions()

            # Convert current positions to comparable format
            current_dict = {}
            for pos in current_positions:
                if pos.get('contracts', 0) != 0:  # Only non-zero positions
                    symbol = pos['symbol']
                    size = float(pos['contracts'])
                    side = pos['side']
                    signed_size = size if side == 'long' else -size
                    current_dict[symbol] = signed_size

            # Convert target positions to comparable format
            target_dict = {}
            for target_pos in target_positions:
                symbol = target_pos['symbol']
                size = target_pos['size_native']
                side = target_pos['side']
                signed_size = size if side == 'long' else -size
                target_dict[symbol] = signed_size

            # Get tolerance from config (using new standardized parameter)
            tolerance_pct = self.config.get('buffer_zone_tolerance_percentage', 5.0) / 100  # Convert percentage to decimal

            # Check for mismatches
            mismatches = []
            all_symbols = set(current_dict.keys()) | set(target_dict.keys())

            for symbol in all_symbols:
                current_size = current_dict.get(symbol, 0)
                target_size = target_dict.get(symbol, 0)

                # Calculate absolute tolerance based on target size
                if target_size != 0:
                    tolerance = abs(target_size) * tolerance_pct
                else:
                    tolerance = tolerance_pct  # Small absolute tolerance for zero targets

                difference = abs(current_size - target_size)

                if difference > tolerance:
                    mismatches.append({
                        'symbol': symbol,
                        'current': current_size,
                        'target': target_size,
                        'difference': difference,
                        'tolerance': tolerance,
                        'percentage_diff': (difference / abs(target_size)) * 100 if target_size != 0 else float('inf')
                    })

            # Log results
            if mismatches:
                logger.warning(f"❌ Found {len(mismatches)} position mismatches (tolerance: {tolerance_pct*100:.2f}%):")
                for mismatch in mismatches:
                    logger.warning(f"   {mismatch['symbol']}: current={mismatch['current']:.6f}, "
                                 f"target={mismatch['target']:.6f}, "
                                 f"diff={mismatch['difference']:.6f} "
                                 f"({mismatch['percentage_diff']:.2f}%)")
                return False
            else:
                logger.info(f"✅ All positions match target within {tolerance_pct*100:.2f}% tolerance")
                logger.info(f"📊 Checked {len(all_symbols)} symbols: {len(current_dict)} current, {len(target_dict)} target")
                return True

        except Exception as e:
            logger.error(f"❌ Error checking position match: {e}")
            import traceback
            logger.debug(f"Full position check error traceback: {traceback.format_exc()}")
            return False


    
    async def _cleanup(self):
        """Cleanup resources and save final state"""
        try:
            logger.info("🧹 Cleaning up strategy resources...")

            # Cancel any open orders if executor is available
            if self.executor:
                try:
                    logger.info("🚫 Canceling any remaining open orders...")
                    await self.executor._cancel_all_open_orders()
                except Exception as e:
                    logger.warning(f"⚠️ Failed to cancel orders during cleanup: {e}")

            # Save final state
            if self.state_manager:
                try:
                    logger.info("💾 Saving final state...")
                    self.state_manager.save_state()
                except Exception as e:
                    logger.error(f"❌ Failed to save final state: {e}")

            # Log performance summary
            if self.performance_monitor:
                try:
                    self.performance_monitor.log_summary()
                except Exception as e:
                    logger.warning(f"⚠️ Failed to log performance summary: {e}")

            # Close exchange connection
            if self.exchange:
                try:
                    logger.info("🔌 Closing exchange connection...")
                    if hasattr(self.exchange, 'close'):
                        await self.exchange.close()
                except Exception as e:
                    logger.warning(f"⚠️ Failed to close exchange connection: {e}")

            logger.info("✅ Strategy cleanup completed")

        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
    
    def stop(self):
        """Stop the strategy gracefully"""
        logger.info("🛑 Stopping strategy...")
        self.running = False
