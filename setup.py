#!/usr/bin/env python3

"""
Setup script for StatArb Carry Trade Strategy
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """Install required dependencies"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_config():
    """Set up configuration file"""
    config_file = Path("config.yaml")
    example_file = Path("config.example.yaml")
    
    if config_file.exists():
        print("Configuration file already exists")
        return True

    if example_file.exists():
        print("Creating configuration file from example...")
        shutil.copy(example_file, config_file)
        print("Configuration file created")
        print("Please edit config.yaml with your settings")
        return True
    else:
        print("Example configuration file not found")
        return False


def check_environment():
    """Check environment variables"""
    env_vars = [
        "BYBIT_API_KEY", "BYBIT_API_SECRET",
        "BINANCE_API_KEY", "BINANCE_API_SECRET",
        "OKX_API_KEY", "OKX_API_SECRET", "OKX_PASSPHRASE",
        "HYPERLIQUID_WALLET_ADDRESS", "HYPERLIQUID_PRIVATE_KEY",
        "EXCHANGE", "TOTAL_CAPITAL", "USE_TESTNET", "USE_DEMO", "SIMULATION_MODE"
    ]

    found_vars = []
    for var in env_vars:
        if os.getenv(var):
            found_vars.append(var)

    if found_vars:
        print(f"Found environment variables: {', '.join(found_vars)}")
    else:
        print("No environment variables found - will use interactive setup")

    return True


def create_directories():
    """Create necessary directories"""
    directories = ["logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    return True


def run_tests():
    """Run basic tests if available"""
    test_file = Path("tests") / "test_basic.py"
    if test_file.exists():
        try:
            subprocess.check_call([sys.executable, "-m", "pytest", "tests/", "-v"])
            return True
        except subprocess.CalledProcessError:
            print("Some tests failed - check output above")
            return False
    return True


def main():
    """Main setup function"""
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Setting up configuration", setup_config),
        ("Checking environment", check_environment),
        ("Creating directories", create_directories),
        ("Running tests", run_tests),
    ]

    failed_steps = []

    for step_name, step_func in steps:
        if not step_func():
            failed_steps.append(step_name)
    
    print("\n" + "=" * 50)
    
    if failed_steps:
        print("Setup completed with issues:")
        for step in failed_steps:
            print(f"   {step}")
        print("\nPlease resolve the issues above before running the strategy.")
        return False
    else:
        print("Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit config.yaml with your settings")
        print("2. Set up your API credentials (interactive or environment variables)")
        print("3. Run: python main.py")
        print("\nFor help, see README.md")
        return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
