"""
Performance tracking and attribution for multi-strategy portfolios

This module tracks performance metrics for individual strategies and the combined portfolio,
enabling detailed performance attribution analysis.
"""

import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import json

logger = logging.getLogger(__name__)


@dataclass
class StrategyPerformance:
    """Performance metrics for a single strategy"""
    strategy_name: str
    timestamp: datetime
    positions_count: int
    capital_allocated: float
    target_positions: List[Dict[str, Any]] = field(default_factory=list)
    execution_time_seconds: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    
    # Performance metrics (to be calculated later with actual fills)
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    total_pnl: float = 0.0
    
    # Risk metrics
    portfolio_volatility: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    
    # Attribution metrics
    contribution_to_portfolio: float = 0.0  # Strategy's contribution to total portfolio performance
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'strategy_name': self.strategy_name,
            'timestamp': self.timestamp.isoformat(),
            'positions_count': self.positions_count,
            'capital_allocated': self.capital_allocated,
            'target_positions': self.target_positions,
            'execution_time_seconds': self.execution_time_seconds,
            'success': self.success,
            'error_message': self.error_message,
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'total_pnl': self.total_pnl,
            'portfolio_volatility': self.portfolio_volatility,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'contribution_to_portfolio': self.contribution_to_portfolio
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StrategyPerformance':
        """Create from dictionary"""
        data = data.copy()
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


class PerformanceTracker:
    """
    Tracks performance metrics for individual strategies and combined portfolio
    
    This class maintains historical performance data and provides methods for
    calculating attribution metrics and generating performance reports.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize performance tracker
        
        Args:
            config: Configuration containing tracking settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Performance history storage
        self.strategy_history: Dict[str, List[StrategyPerformance]] = {}
        self.portfolio_history: List[Dict[str, Any]] = []
        
        # Configuration
        self.max_history_days = config.get('max_performance_history_days', 90)
        self.enable_detailed_tracking = config.get('enable_detailed_performance_tracking', True)
        self.performance_file = config.get('performance_tracking_file', 'performance_history.json')
        
        self.logger.info(f"🏗️ Performance tracker initialized")
        self.logger.info(f"   Max history: {self.max_history_days} days")
        self.logger.info(f"   Detailed tracking: {'enabled' if self.enable_detailed_tracking else 'disabled'}")
        
        # Load existing performance data
        self._load_performance_history()
    
    def record_strategy_performance(self, strategy_name: str, strategy_result, 
                                  final_positions: List[Any]) -> None:
        """
        Record performance metrics for a strategy execution
        
        Args:
            strategy_name: Name of the strategy
            strategy_result: StrategyResult object from strategy execution
            final_positions: Final positions allocated to this strategy in combined portfolio
        """
        try:
            # Calculate capital allocated to this strategy in final portfolio
            strategy_capital = sum(
                pos.strategy_contributions.get(strategy_name, 0) 
                for pos in final_positions
                if hasattr(pos, 'strategy_contributions')
            )
            
            # Create performance record
            performance = StrategyPerformance(
                strategy_name=strategy_name,
                timestamp=datetime.now(timezone.utc),
                positions_count=strategy_result.position_count,
                capital_allocated=strategy_capital,
                target_positions=[
                    {
                        'symbol': pos.symbol,
                        'side': pos.side,
                        'size_usd': pos.size_usd,
                        'size_native': pos.size_native,
                        'weight': pos.weight,
                        'confidence': pos.confidence
                    }
                    for pos in strategy_result.target_positions
                ],
                execution_time_seconds=strategy_result.execution_time_seconds,
                success=strategy_result.success,
                error_message=strategy_result.error_message
            )
            
            # Add to history
            if strategy_name not in self.strategy_history:
                self.strategy_history[strategy_name] = []
            
            self.strategy_history[strategy_name].append(performance)
            
            # Clean old history
            self._cleanup_old_history(strategy_name)
            
            self.logger.info(f"📊 Recorded performance for {strategy_name}: "
                           f"{performance.positions_count} positions, "
                           f"${performance.capital_allocated:,.0f} allocated")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record strategy performance for {strategy_name}: {e}")
    
    def record_portfolio_performance(self, combined_positions: List[Any], 
                                   strategy_results: Dict[str, Any],
                                   execution_metrics: Dict[str, Any]) -> None:
        """
        Record performance metrics for the combined portfolio
        
        Args:
            combined_positions: Final combined portfolio positions
            strategy_results: Results from all strategies
            execution_metrics: Metrics from portfolio execution
        """
        try:
            total_capital = sum(pos.size_usd for pos in combined_positions)
            
            portfolio_record = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'total_positions': len(combined_positions),
                'total_capital': total_capital,
                'long_positions': len([pos for pos in combined_positions if pos.side == 'long']),
                'short_positions': len([pos for pos in combined_positions if pos.side == 'short']),
                'long_capital': sum(pos.size_usd for pos in combined_positions if pos.side == 'long'),
                'short_capital': sum(pos.size_usd for pos in combined_positions if pos.side == 'short'),
                'contributing_strategies': list(set().union(*[pos.contributing_strategies for pos in combined_positions])),
                'strategy_count': len([r for r in strategy_results.values() if r.success]),
                'execution_metrics': execution_metrics,
                'positions': [
                    {
                        'symbol': pos.symbol,
                        'side': pos.side,
                        'size_usd': pos.size_usd,
                        'contributing_strategies': pos.contributing_strategies,
                        'strategy_contributions': pos.strategy_contributions
                    }
                    for pos in combined_positions
                ] if self.enable_detailed_tracking else []
            }
            
            self.portfolio_history.append(portfolio_record)
            
            # Clean old portfolio history
            self._cleanup_old_portfolio_history()
            
            self.logger.info(f"📊 Recorded portfolio performance: "
                           f"{len(combined_positions)} positions, "
                           f"${total_capital:,.0f} total capital")
            
            # Save to file
            self._save_performance_history()
            
        except Exception as e:
            self.logger.error(f"❌ Failed to record portfolio performance: {e}")
    
    def get_strategy_performance_summary(self, strategy_name: str, 
                                       days: int = 30) -> Optional[Dict[str, Any]]:
        """
        Get performance summary for a specific strategy
        
        Args:
            strategy_name: Name of the strategy
            days: Number of days to include in summary
            
        Returns:
            Performance summary dictionary or None if no data
        """
        if strategy_name not in self.strategy_history:
            return None
        
        history = self.strategy_history[strategy_name]
        if not history:
            return None
        
        # Filter by date range
        cutoff_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
        
        recent_history = [
            perf for perf in history 
            if perf.timestamp >= cutoff_date
        ]
        
        if not recent_history:
            return None
        
        # Calculate summary metrics
        total_executions = len(recent_history)
        successful_executions = sum(1 for perf in recent_history if perf.success)
        avg_positions = sum(perf.positions_count for perf in recent_history) / total_executions
        avg_capital = sum(perf.capital_allocated for perf in recent_history) / total_executions
        avg_execution_time = sum(perf.execution_time_seconds for perf in recent_history) / total_executions
        
        return {
            'strategy_name': strategy_name,
            'period_days': days,
            'total_executions': total_executions,
            'successful_executions': successful_executions,
            'success_rate': successful_executions / total_executions,
            'avg_positions_per_execution': avg_positions,
            'avg_capital_per_execution': avg_capital,
            'avg_execution_time_seconds': avg_execution_time,
            'latest_execution': recent_history[-1].to_dict(),
            'first_execution': recent_history[0].to_dict()
        }
    
    def get_portfolio_performance_summary(self, days: int = 30) -> Dict[str, Any]:
        """
        Get performance summary for the combined portfolio
        
        Args:
            days: Number of days to include in summary
            
        Returns:
            Portfolio performance summary dictionary
        """
        if not self.portfolio_history:
            return {'total_executions': 0, 'period_days': days}
        
        # Filter by date range
        cutoff_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
        
        recent_history = [
            record for record in self.portfolio_history
            if datetime.fromisoformat(record['timestamp']) >= cutoff_date
        ]
        
        if not recent_history:
            return {'total_executions': 0, 'period_days': days}
        
        # Calculate summary metrics
        total_executions = len(recent_history)
        avg_positions = sum(record['total_positions'] for record in recent_history) / total_executions
        avg_capital = sum(record['total_capital'] for record in recent_history) / total_executions
        avg_strategies = sum(record['strategy_count'] for record in recent_history) / total_executions
        
        return {
            'period_days': days,
            'total_executions': total_executions,
            'avg_positions_per_execution': avg_positions,
            'avg_capital_per_execution': avg_capital,
            'avg_strategies_per_execution': avg_strategies,
            'latest_execution': recent_history[-1],
            'first_execution': recent_history[0]
        }

    def _cleanup_old_history(self, strategy_name: str) -> None:
        """Remove old performance records beyond retention period"""
        if strategy_name not in self.strategy_history:
            return

        cutoff_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - self.max_history_days)

        original_count = len(self.strategy_history[strategy_name])
        self.strategy_history[strategy_name] = [
            perf for perf in self.strategy_history[strategy_name]
            if perf.timestamp >= cutoff_date
        ]

        removed_count = original_count - len(self.strategy_history[strategy_name])
        if removed_count > 0:
            self.logger.debug(f"🧹 Cleaned {removed_count} old performance records for {strategy_name}")

    def _cleanup_old_portfolio_history(self) -> None:
        """Remove old portfolio performance records beyond retention period"""
        cutoff_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - self.max_history_days)

        original_count = len(self.portfolio_history)
        self.portfolio_history = [
            record for record in self.portfolio_history
            if datetime.fromisoformat(record['timestamp']) >= cutoff_date
        ]

        removed_count = original_count - len(self.portfolio_history)
        if removed_count > 0:
            self.logger.debug(f"🧹 Cleaned {removed_count} old portfolio performance records")

    def _save_performance_history(self) -> None:
        """Save performance history to file"""
        try:
            # Prepare data for serialization
            data = {
                'strategy_history': {
                    name: [perf.to_dict() for perf in history]
                    for name, history in self.strategy_history.items()
                },
                'portfolio_history': self.portfolio_history,
                'last_updated': datetime.now(timezone.utc).isoformat()
            }

            # Save to file
            with open(self.performance_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)

            self.logger.debug(f"💾 Saved performance history to {self.performance_file}")

        except Exception as e:
            self.logger.error(f"❌ Failed to save performance history: {e}")

    def _load_performance_history(self) -> None:
        """Load performance history from file"""
        try:
            with open(self.performance_file, 'r') as f:
                data = json.load(f)

            # Load strategy history
            self.strategy_history = {}
            for name, history_data in data.get('strategy_history', {}).items():
                self.strategy_history[name] = [
                    StrategyPerformance.from_dict(perf_data)
                    for perf_data in history_data
                ]

            # Load portfolio history
            self.portfolio_history = data.get('portfolio_history', [])

            total_strategy_records = sum(len(history) for history in self.strategy_history.values())
            self.logger.info(f"📂 Loaded performance history: "
                           f"{len(self.strategy_history)} strategies, "
                           f"{total_strategy_records} strategy records, "
                           f"{len(self.portfolio_history)} portfolio records")

        except FileNotFoundError:
            self.logger.info(f"📂 No existing performance history found, starting fresh")
        except Exception as e:
            self.logger.error(f"❌ Failed to load performance history: {e}")
            self.logger.info(f"📂 Starting with empty performance history")

    def get_all_strategy_names(self) -> List[str]:
        """Get list of all strategies with performance history"""
        return list(self.strategy_history.keys())

    def export_performance_data(self, output_file: str = None) -> str:
        """
        Export all performance data to a file

        Args:
            output_file: Output file path (optional)

        Returns:
            Path to exported file
        """
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"performance_export_{timestamp}.json"

        try:
            # Prepare comprehensive export data
            export_data = {
                'export_timestamp': datetime.now(timezone.utc).isoformat(),
                'strategy_history': {
                    name: [perf.to_dict() for perf in history]
                    for name, history in self.strategy_history.items()
                },
                'portfolio_history': self.portfolio_history,
                'summary_statistics': {
                    'total_strategies': len(self.strategy_history),
                    'total_strategy_executions': sum(len(history) for history in self.strategy_history.values()),
                    'total_portfolio_executions': len(self.portfolio_history),
                    'date_range': {
                        'earliest': min(
                            [perf.timestamp.isoformat() for history in self.strategy_history.values() for perf in history] +
                            [record['timestamp'] for record in self.portfolio_history]
                        ) if self.strategy_history or self.portfolio_history else None,
                        'latest': max(
                            [perf.timestamp.isoformat() for history in self.strategy_history.values() for perf in history] +
                            [record['timestamp'] for record in self.portfolio_history]
                        ) if self.strategy_history or self.portfolio_history else None
                    }
                }
            }

            with open(output_file, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)

            self.logger.info(f"📤 Exported performance data to {output_file}")
            return output_file

        except Exception as e:
            self.logger.error(f"❌ Failed to export performance data: {e}")
            raise
