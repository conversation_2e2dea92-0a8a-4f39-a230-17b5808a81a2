"""
Simple credential storage (no encryption for ease of use)
"""

import json
import logging
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class SecureStorage:
    """Simple storage for credentials (no encryption for ease of use)"""

    def __init__(self, storage_path: str = "credentials.json"):
        self.storage_path = Path(storage_path)

    def store_credentials(self, credentials: Dict[str, Any]):
        """Store credentials in plain JSON"""
        try:
            # Write to file
            self.storage_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.storage_path, 'w') as f:
                json.dump(credentials, f, indent=2)

            logger.info(f"📁 Credentials stored in {self.storage_path}")

        except Exception as e:
            logger.error(f"❌ Failed to store credentials: {e}")
            raise

    def load_credentials(self) -> Dict[str, Any]:
        """Load credentials from JSON file"""
        try:
            if not self.storage_path.exists():
                logger.debug("📁 No credentials file found")
                return {}

            with open(self.storage_path, 'r') as f:
                credentials = json.load(f)

            logger.info(f"📁 Credentials loaded from {self.storage_path}")
            return credentials

        except Exception as e:
            logger.error(f"❌ Failed to load credentials: {e}")
            return {}

    def update_credential(self, key: str, value: str):
        """Update a single credential"""
        credentials = self.load_credentials()
        credentials[key] = value
        self.store_credentials(credentials)

    def delete_credentials(self):
        """Delete credentials file"""
        try:
            if self.storage_path.exists():
                self.storage_path.unlink()
                logger.info("🗑️ Credentials file deleted")
        except Exception as e:
            logger.error(f"❌ Failed to delete credentials: {e}")

    def has_credentials(self) -> bool:
        """Check if credentials file exists"""
        return self.storage_path.exists()


def setup_credentials_interactively() -> Dict[str, Any]:
    """Interactive setup for credentials"""
    print("\n🔧 Credential Setup")
    print("=" * 40)

    credentials = {}

    # Exchange selection
    exchange = input("Exchange (bybit/binance/okx/hyperliquid) [bybit]: ").strip().lower() or 'bybit'
    credentials['exchange'] = exchange

    # API credentials
    api_key = input(f"{exchange.title()} API Key: ").strip()
    api_secret = input(f"{exchange.title()} API Secret: ").strip()

    if api_key and api_secret:
        credentials['api_key'] = api_key
        credentials['api_secret'] = api_secret

    # Trading mode
    use_testnet = input("Use testnet? (y/n) [y]: ").strip().lower()
    credentials['use_testnet'] = use_testnet in ('', 'y', 'yes', 'true')

    use_demo = input("Use demo trading? (y/n) [n]: ").strip().lower()
    credentials['use_demo'] = use_demo in ('y', 'yes', 'true')

    # Capital
    capital = input("Total capital USD [10000]: ").strip()
    try:
        credentials['total_capital_usd'] = float(capital) if capital else 10000
    except ValueError:
        credentials['total_capital_usd'] = 10000

    # Simulation mode
    simulation = input("Enable simulation mode (no real trades)? (y/n) [y]: ").strip().lower()
    credentials['simulation_mode'] = simulation in ('', 'y', 'yes', 'true')

    return credentials
